/* IMPORT */
/* MAIN */
const isException = (value) => {
    return (value instanceof Error) && ('code' in value);
};
const isFunction = (value) => {
    return (typeof value === 'function');
};
const isString = (value) => {
    return (typeof value === 'string');
};
const isUndefined = (value) => {
    return (value === undefined);
};
/* EXPORT */
export { isException, isFunction, isString, isUndefined };

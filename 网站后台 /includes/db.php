<?php
/**
 * 数据库连接 - 支持多种环境的优化版本
 * 支持本地开发和服务器部署环境
 */

// 数据库配置 - 支持多种环境
$db_configs = [
    // 服务器环境配置
    'production' => [
        'host' => '127.0.0.1',
        'port' => '3306',
        'dbname' => 'xiaomeihuakefu_c',
        'username' => 'xiaomeihuakefu_c',
        'password' => '7Da5F1Xx995cxYz8',
        'charset' => 'utf8mb4'
    ],
    // 本地开发环境配置
    'development' => [
        'host' => '127.0.0.1',
        'port' => '3306',
        'dbname' => 'xiaomeihua_local',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ],
    // SQLite备用配置（用于测试）
    'sqlite' => [
        'type' => 'sqlite',
        'path' => __DIR__ . '/../data/app_settings.db'
    ]
];

// 强制使用生产环境MySQL配置
$environment = 'production';
$db_config = $db_configs[$environment];

// 确保使用MySQL数据库
error_log("使用MySQL数据库配置: {$db_config['host']}:{$db_config['port']}/{$db_config['dbname']}");

$connection_success = false;
$pdo = null;

try {
    // 只使用MySQL连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
        PDO::ATTR_TIMEOUT => 10
    ]);

    // 设置时区
    $pdo->exec("SET time_zone = '+08:00'");

    $connection_success = true;
    error_log("MySQL数据库连接成功 - {$environment}环境 - {$db_config['dbname']}");

} catch (PDOException $e) {
    // MySQL连接失败
    error_log("MySQL数据库连接失败: " . $e->getMessage());
    error_log("连接参数: host={$db_config['host']}, port={$db_config['port']}, dbname={$db_config['dbname']}, user={$db_config['username']}");
    $connection_success = false;
    $pdo = null;
}

/**
 * 安全的数据库查询函数
 */
function db_query($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("SQL执行失败: " . $e->getMessage() . " - SQL: " . $sql);
        throw $e;
    }
}

function db_get_row($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->fetch();
}

function db_get_all($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->fetchAll();
}

function db_execute($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->rowCount();
}

function db_last_insert_id() {
    global $pdo;
    return $pdo->lastInsertId();
}

function is_database_available() {
    global $connection_success;
    return $connection_success;
}

// 测试数据库连接
if ($connection_success) {
    try {
        // 测试查询
        $test_query = $pdo->query("SELECT 1 as test");
        $result = $test_query->fetch();
        if ($result['test'] == 1) {
            error_log("数据库连接测试成功");
        }
    } catch (PDOException $e) {
        error_log("数据库连接测试失败: " . $e->getMessage());
    }
}

?>
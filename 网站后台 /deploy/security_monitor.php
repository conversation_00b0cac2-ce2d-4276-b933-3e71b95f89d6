<?php
// 安全监控模块 - 登录通知和页面访问监控
class SecurityMonitor {
    private $pdo;
    private $settings;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->settings = getSystemSettings();
    }
    
    /**
     * 检查设备是否为信任设备
     */
    public function isTrustedDevice($user_id, $device_fingerprint) {
        try {
            $stmt = $this->pdo->prepare("SELECT id FROM trusted_devices WHERE user_id = ? AND device_fingerprint = ? AND is_active = 1");
            $stmt->execute([$user_id, $device_fingerprint]);
            return $stmt->fetchColumn() !== false;
        } catch (Exception $e) {
            error_log('检查信任设备失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 记录页面访问日志
     */
    public function logPageAccess($user_id, $page, $is_trusted_device, $device_info = []) {
        try {
            // 创建页面访问日志表（如果不存在）
            $this->createPageAccessLogTable();
            
            $stmt = $this->pdo->prepare("
                INSERT INTO page_access_logs 
                (user_id, page_name, ip_address, user_agent, device_fingerprint, is_trusted_device, access_time, device_info) 
                VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)
            ");
            
            $stmt->execute([
                $user_id,
                $page,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $device_info['fingerprint'] ?? '',
                $is_trusted_device ? 1 : 0,
                json_encode($device_info)
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log('记录页面访问失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 创建页面访问日志表
     */
    private function createPageAccessLogTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS `page_access_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `page_name` varchar(100) NOT NULL,
                `ip_address` varchar(45) NOT NULL,
                `user_agent` text,
                `device_fingerprint` varchar(255) DEFAULT NULL,
                `is_trusted_device` tinyint(1) NOT NULL DEFAULT 0,
                `access_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `device_info` text,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `page_name` (`page_name`),
                KEY `access_time` (`access_time`),
                KEY `is_trusted_device` (`is_trusted_device`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
            
            $this->pdo->exec($sql);
        } catch (Exception $e) {
            error_log('创建页面访问日志表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送登录通知邮件（优化版）
     */
    public function sendLoginNotification($user_email, $login_info) {
        if (($this->settings['login_notification_enabled'] ?? '0') !== '1') {
            return true; // 未启用，直接返回成功
        }
        
        $email_data = [
            'type' => 'login_notification',
            'data' => [
                'email' => $user_email,
                'ip' => $login_info['ip'],
                'time' => $login_info['time'],
                'user_agent' => $login_info['user_agent'] ?? '',
                'device_name' => $login_info['device_name'] ?? '',
                'is_trusted' => $login_info['is_trusted'] ?? false,
                'location' => $this->getLocationByIP($login_info['ip'])
            ]
        ];
        
        return $this->sendAsyncEmail($email_data);
    }
    
    /**
     * 发送陌生设备访问警告邮件
     */
    public function sendUntrustedAccessAlert($user_email, $access_info) {
        if (($this->settings['login_notification_enabled'] ?? '0') !== '1') {
            return true; // 未启用，直接返回成功
        }
        
        $email_data = [
            'type' => 'untrusted_access_alert',
            'data' => [
                'email' => $user_email,
                'ip' => $access_info['ip'],
                'time' => $access_info['time'],
                'page' => $access_info['page'],
                'user_agent' => $access_info['user_agent'] ?? '',
                'device_fingerprint' => $access_info['device_fingerprint'] ?? '',
                'location' => $this->getLocationByIP($access_info['ip'])
            ]
        ];
        
        return $this->sendAsyncEmail($email_data);
    }
    
    /**
     * 异步发送邮件
     */
    private function sendAsyncEmail($email_data) {
        try {
            // 使用非阻塞cURL发送到异步处理脚本
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . '/api/security_email_sender.php',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($email_data),
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'X-Internal-Call: true',
                    'X-Security-Token: xiaomeihua_security_2024'
                ],
                CURLOPT_TIMEOUT => 1,
                CURLOPT_CONNECTTIMEOUT => 1,
                CURLOPT_RETURNTRANSFER => false,
                CURLOPT_NOSIGNAL => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            // 在后台执行
            register_shutdown_function(function() use ($ch) {
                @curl_exec($ch);
                @curl_close($ch);
            });
            
            return true;
        } catch (Exception $e) {
            error_log('异步邮件发送失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取IP地理位置
     */
    private function getLocationByIP($ip) {
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return '本地环境';
        }
        
        try {
            $response = @file_get_contents("http://ip-api.com/json/{$ip}?lang=zh-CN", false, stream_context_create([
                'http' => ['timeout' => 3]
            ]));
            
            if ($response) {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    return $data['country'] . ' ' . $data['regionName'] . ' ' . $data['city'];
                }
            }
        } catch (Exception $e) {
            // 忽略错误
        }
        
        return '未知地点';
    }
    
    /**
     * 检查页面访问安全性（优化版）
     */
    public function checkPageSecurity($user_id, $page) {
        error_log("🔍 安全检查开始 - 用户ID: $user_id, 页面: $page");
        
        // 🔧 紧急修复：如果是验证码相关的操作，直接允许
        if (isset($_POST['action']) && in_array($_POST['action'], ['trust_device', 'send_trust_device_code', 'send_smtp_config_code'])) {
            error_log("✅ 验证码相关操作，直接允许访问");
            return true;
        }
        
        // 检查是否启用了邮箱验证
        if (($this->settings['email_verification_enabled'] ?? '0') !== '1') {
            error_log("✅ 邮箱验证未启用，允许访问");
            return true; // 如果未启用邮箱验证，允许所有访问
        }
        
        // 敏感页面列表
        $sensitive_pages = ['dashboard', 'keys', 'scripts', 'users', 'analytics', 'settings'];
        
        if (!in_array($page, $sensitive_pages)) {
            error_log("✅ 非敏感页面，允许访问");
            return true; // 非敏感页面，允许访问
        }
        
        $device_fingerprint = getDeviceFingerprint();
        error_log("🔍 设备指纹: $device_fingerprint");
        
        // 🔧 关键修复1：检查是否刚刚通过验证码验证成功（优先级最高）
        if (isset($_SESSION['device_trusted_this_session']) && 
            $_SESSION['device_trusted_this_session'] === true) {
            
            // 检查设备指纹是否匹配或者时间是否在有效期内
            $fingerprint_match = isset($_SESSION['trusted_device_fingerprint']) && 
                                 $_SESSION['trusted_device_fingerprint'] === $device_fingerprint;
            
            $time_valid = isset($_SESSION['trusted_device_verified_at']) &&
                         (time() - $_SESSION['trusted_device_verified_at']) < 3600; // 1小时内有效
            
            if ($fingerprint_match && $time_valid) {
                error_log("✅ 刚刚验证成功的设备，直接允许访问");
                return true;
            } else if ($time_valid) {
                // 时间有效但指纹不匹配，可能是指纹算法问题，仍然允许
                error_log("⚠️ 时间有效但指纹不完全匹配，仍然允许访问");
                error_log("会话指纹: " . ($_SESSION['trusted_device_fingerprint'] ?? 'null'));
                error_log("当前指纹: $device_fingerprint");
                return true;
            }
        }
        
        // 🔧 关键修复2：检查是否通过邮箱验证登录（第二优先级）
        if (isset($_SESSION['verified_by_email']) && $_SESSION['verified_by_email'] === true) {
            error_log("✅ 通过邮箱验证登录，允许访问");
            return true; // 通过邮箱验证的用户，允许访问
        }
        
        // 🔧 关键修复3：检查数据库中的信任设备记录
        $is_trusted = $this->isTrustedDevice($user_id, $device_fingerprint);
        error_log("🔍 数据库信任设备检查结果: " . ($is_trusted ? '是' : '否'));
        
        // 如果是数据库中的信任设备，允许访问
        if ($is_trusted) {
            error_log("✅ 数据库中的信任设备，允许访问");
            // 同时设置会话标志，避免重复检查
            $_SESSION['device_trusted_this_session'] = true;
            $_SESSION['trusted_device_fingerprint'] = $device_fingerprint;
            $_SESSION['trusted_device_verified_at'] = time();
            return true;
        }
        
        // 🔧 关键修复4：检查用户是否完成了完整的安全配置
        $has_complete_setup = $this->hasCompleteSecuritySetup($user_id);
        error_log("🔍 完整安全配置检查结果: " . ($has_complete_setup ? '已完成' : '未完成'));
        
        if (!$has_complete_setup) {
            error_log("✅ 未完成完整安全配置，允许访问以便用户完成配置");
            return true; // 未完成完整安全配置，允许访问以便用户完成配置
        }
        
        // 🔧 关键修复5：如果正在进行信任设备操作，给予宽限期
        if ($page === 'settings' && isset($_SESSION['trust_device_operation_time']) &&
            (time() - $_SESSION['trust_device_operation_time']) < 300) { // 5分钟宽限期
            error_log("✅ 信任设备操作宽限期内，允许访问");
            return true;
        }
        
        // 记录访问日志
        $device_info = [
            'fingerprint' => $device_fingerprint,
            'name' => getDeviceName(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->logPageAccess($user_id, $page, $is_trusted, $device_info);
        
        // 最后才触发安全防护
        error_log("❌ 陌生设备且未通过任何验证，触发安全防护");
        error_log("会话信息: " . json_encode([
            'device_trusted_this_session' => $_SESSION['device_trusted_this_session'] ?? 'not_set',
            'trusted_device_fingerprint' => $_SESSION['trusted_device_fingerprint'] ?? 'not_set',
            'verified_by_email' => $_SESSION['verified_by_email'] ?? 'not_set',
            'current_fingerprint' => $device_fingerprint,
            'POST_action' => $_POST['action'] ?? 'not_set'
        ]));
        
        $this->handleIntruderAccess($user_id, $page, $device_info);
        return false;
    }
    
    /**
     * 检查用户是否完成了完整的安全配置
     * 需要同时满足：有SMTP配置 AND 有信任设备
     */
    private function hasCompleteSecuritySetup($user_id) {
        try {
            // 检查用户是否有SMTP配置
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM smtp_config WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $has_smtp_config = $stmt->fetchColumn() > 0;
            
            // 检查用户是否有信任设备
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM trusted_devices WHERE user_id = ? AND is_active = 1");
            $stmt->execute([$user_id]);
            $has_trusted_devices = $stmt->fetchColumn() > 0;
            
            // 只有同时有SMTP配置AND信任设备才算完成安全配置
            return $has_smtp_config && $has_trusted_devices;
        } catch (Exception $e) {
            error_log('检查安全配置状态失败: ' . $e->getMessage());
            // 出错时为了安全考虑，返回false（未完成配置）
            return false;
        }
    }
    
    /**
     * 处理入侵者访问
     */
    private function handleIntruderAccess($user_id, $page, $device_info) {
        // 获取用户邮箱
        $stmt = $this->pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user_email = $stmt->fetchColumn();
        
        if ($user_email) {
            $access_info = [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'time' => date('Y-m-d H:i:s'),
                'page' => $page,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'device_fingerprint' => $device_info['fingerprint'] ?? '',
                'threat_level' => 'HIGH' // 标记为高威胁
            ];
            
            $this->sendIntruderAlert($user_email, $access_info);
        }
        
        // 记录入侵尝试
        $this->logIntrusionAttempt($user_id, $device_info);
    }
    
    /**
     * 发送入侵者警告邮件
     */
    public function sendIntruderAlert($user_email, $access_info) {
        if (($this->settings['login_notification_enabled'] ?? '0') !== '1') {
            return true;
        }
        
        $email_data = [
            'type' => 'intruder_alert',
            'data' => [
                'email' => $user_email,
                'ip' => $access_info['ip'],
                'time' => $access_info['time'],
                'page' => $access_info['page'],
                'user_agent' => $access_info['user_agent'] ?? '',
                'device_fingerprint' => $access_info['device_fingerprint'] ?? '',
                'threat_level' => $access_info['threat_level'] ?? 'MEDIUM',
                'location' => $this->getLocationByIP($access_info['ip'])
            ]
        ];
        
        return $this->sendAsyncEmail($email_data);
    }
    
    /**
     * 记录入侵尝试
     */
    private function logIntrusionAttempt($user_id, $device_info) {
        try {
            // 创建入侵日志表（如果不存在）
            $this->createIntrusionLogTable();
            
            $stmt = $this->pdo->prepare("
                INSERT INTO intrusion_logs 
                (user_id, ip_address, user_agent, device_fingerprint, attempt_time, device_info) 
                VALUES (?, ?, ?, ?, NOW(), ?)
            ");
            
            $stmt->execute([
                $user_id,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $device_info['fingerprint'] ?? '',
                json_encode($device_info)
            ]);
            
        } catch (Exception $e) {
            error_log('记录入侵尝试失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建入侵日志表
     */
    private function createIntrusionLogTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS `intrusion_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `ip_address` varchar(45) NOT NULL,
                `user_agent` text,
                `device_fingerprint` varchar(255) DEFAULT NULL,
                `attempt_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `device_info` text,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `attempt_time` (`attempt_time`),
                KEY `ip_address` (`ip_address`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
            
            $this->pdo->exec($sql);
        } catch (Exception $e) {
            error_log('创建入侵日志表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取信任设备列表
     */
    public function getTrustedDevices($user_id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT device_name, device_fingerprint, ip_address, created_at, updated_at
                FROM trusted_devices 
                WHERE user_id = ? AND is_active = 1 
                ORDER BY updated_at DESC
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log('获取信任设备列表失败: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取最近的页面访问记录
     */
    public function getRecentPageAccess($user_id, $limit = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT page_name, ip_address, is_trusted_device, access_time, device_info
                FROM page_access_logs 
                WHERE user_id = ? 
                ORDER BY access_time DESC 
                LIMIT ?
            ");
            $stmt->execute([$user_id, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log('获取页面访问记录失败: ' . $e->getMessage());
            return [];
        }
    }
}

// 全局安全监控实例
function getSecurityMonitor() {
    global $pdo;
    static $security_monitor = null;
    
    if ($security_monitor === null) {
        $security_monitor = new SecurityMonitor($pdo);
    }
    
    return $security_monitor;
}
?> 
-- 上架产品功能清理脚本
-- 版本: v1.0.0
-- 日期: 2024-01-20
-- 功能: 删除上架产品功能相关的数据库表

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ====================================
-- 删除上架产品功能表
-- ====================================

-- 删除上架产品使用记录表
DROP TABLE IF EXISTS `product_listing_logs`;

-- 删除上架产品配置表
DROP TABLE IF EXISTS `product_listing_configs`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ====================================
-- 清理完成信息
-- ====================================

SELECT 
    '上架产品功能已清理完成！' as '清理状态',
    '已删除 product_listing_configs 表' as '删除表1',
    '已删除 product_listing_logs 表' as '删除表2',
    '系统已恢复到原始状态' as '系统状态'; 
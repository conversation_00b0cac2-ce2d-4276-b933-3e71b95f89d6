<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 尝试加载安全监控，如果失败则使用简化版本
try {
    if (file_exists('../includes/security_monitor.php')) {
        require_once '../includes/security_monitor.php';
    }
} catch (Exception $e) {
    error_log("安全监控加载失败: " . $e->getMessage());
}

// 如果安全监控函数不存在，创建简化版本
if (!function_exists('getSecurityMonitor')) {
    function getSecurityMonitor() {
        return new class {
            public function checkPageSecurity($user_id, $page) {
                return true;
            }
            public function isTrustedDevice($user_id, $device_fingerprint) {
                return true;
            }
            public function sendLoginNotification($email, $login_info) {
                return true;
            }
        };
    }
}

// 检查是否已登录
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

// Flash消息处理函数
function getFlashMessage() {
    $messages = [];
    
    if (isset($_SESSION['flash_success'])) {
        $messages['success'] = $_SESSION['flash_success'];
        unset($_SESSION['flash_success']);
    }
    
    if (isset($_SESSION['flash_error'])) {
        $messages['error'] = $_SESSION['flash_error'];
        unset($_SESSION['flash_error']);
    }
    
    if (isset($_SESSION['flash_warning'])) {
        $messages['warning'] = $_SESSION['flash_warning'];
        unset($_SESSION['flash_warning']);
    }
    
    return $messages;
}

// POST重定向函数
function handleLoginRedirect($success_message = '', $error_message = '', $show_email_verify = false) {
    // 将消息存储到session中
    if (!empty($success_message)) {
        $_SESSION['flash_success'] = $success_message;
    }
    if (!empty($error_message)) {
        $_SESSION['flash_error'] = $error_message;
    }
    if ($show_email_verify) {
        $_SESSION['show_email_verify'] = true;
    }
    
    // 重定向到登录页面
    header("Location: login.php");
    exit;
}

// 检查系统是否已完全配置
function isSystemFullyConfigured() {
    global $pdo;
    
    try {
        // 检查SMTP配置
        $settings = getSystemSettings();
        $smtp_configured = !empty($settings['smtp_host']) && 
                          !empty($settings['smtp_username']) && 
                          !empty($settings['smtp_password']);
        
        // 检查管理员邮箱
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = 1");
        $stmt->execute();
        $admin_email = $stmt->fetchColumn();
        $email_configured = !empty($admin_email);
        
        return [
            'smtp_configured' => $smtp_configured,
            'email_configured' => $email_configured,
            'fully_configured' => $smtp_configured && $email_configured
        ];
    } catch (Exception $e) {
        return [
            'smtp_configured' => false,
            'email_configured' => false,
            'fully_configured' => false
        ];
    }
}

// 获取flash消息
$flash_messages = getFlashMessage();
$error_message = $flash_messages['error'] ?? '';
$success_message = $flash_messages['success'] ?? '';
$warning_message = $flash_messages['warning'] ?? '';
$show_email_verify = isset($_SESSION['show_email_verify']);

// 清除邮箱验证显示标志
if (isset($_SESSION['show_email_verify'])) {
    unset($_SESSION['show_email_verify']);
}

// 检查系统配置状态
$system_config = isSystemFullyConfigured();

// 处理登录请求
if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $email_code = trim($_POST['email_code'] ?? '');
    $trust_device = isset($_POST['trust_device']) ? 1 : 0;
    
    if (empty($username) || empty($password)) {
        handleLoginRedirect('', '请输入用户名和密码');
    } else {
        try {
            // 验证用户名和密码
            $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ? AND password = ?");
            $stmt->execute([$username, md5($password)]);
            $user = $stmt->fetch();
            
            if ($user) {
                // 重新检查系统配置状态
                $system_config = isSystemFullyConfigured();
                
                // 获取系统设置
                $settings = getSystemSettings();
                $email_verification_enabled = ($settings['email_verification_enabled'] ?? '0') === '1';
                
                // 检查用户是否有邮箱设置
                $user_has_email = !empty($user['email']);
                
                // 如果系统未完全配置、邮箱验证未启用或用户没有邮箱，直接登录
                if (!$system_config['fully_configured'] || !$email_verification_enabled || !$user_has_email) {
                    // 设置相应的欢迎消息
                    if (!$system_config['smtp_configured']) {
                        $_SESSION['flash_success'] = '欢迎使用小梅花AI客服系统！请在系统设置中配置SMTP邮箱以启用完整的安全功能。';
                    } elseif (!$system_config['email_configured']) {
                        $_SESSION['flash_success'] = '欢迎回来！建议在系统设置中配置管理员邮箱以获得更好的安全保护。';
                    } elseif (!$email_verification_enabled) {
                        $_SESSION['flash_success'] = '登录成功！邮箱验证功能已关闭。';
                    } elseif (!$user_has_email) {
                        $_SESSION['flash_success'] = '登录成功！建议设置邮箱地址以启用安全验证功能。';
                    }
                    
                    loginUser($user, $trust_device, getDeviceFingerprint());
                } else {
                    // 系统已完全配置，使用完整的验证流程
                    $device_fingerprint = getDeviceFingerprint();
                    
                    // 检查是否为信任设备
                    $stmt = $pdo->prepare("SELECT * FROM trusted_devices WHERE user_id = ? AND device_fingerprint = ? AND is_active = 1");
                    $stmt->execute([$user['id'], $device_fingerprint]);
                    $trusted_device = $stmt->fetch();
                    
                    if ($trusted_device) {
                        // 信任设备，直接登录
                        loginUser($user, $trust_device, $device_fingerprint);
                    } else {
                        // 非信任设备，需要邮箱验证
                        if (empty($email_code)) {
                            // 第一次请求验证码，生成并发送
                            $verification_code = generateVerificationCode();
                            $_SESSION['temp_verification_code'] = $verification_code;
                            $_SESSION['temp_verification_time'] = time();
                            $_SESSION['temp_user_id'] = $user['id'];
                            $_SESSION['temp_trust_device'] = $trust_device;
                            $_SESSION['temp_device_fingerprint'] = $device_fingerprint;
                            $_SESSION['temp_verification_errors'] = 0;
                            
                            // 🔧 自动邮箱修复逻辑 - 开始
                            if ($user['email'] === '<EMAIL>' || empty($user['email'])) {
                                $settings = getSystemSettings();
                                $correct_email = $settings['smtp_username'] ?? '';
                                
                                if (!empty($correct_email) && filter_var($correct_email, FILTER_VALIDATE_EMAIL)) {
                                    // 更新用户邮箱为正确的SMTP用户名
                                    $stmt = $pdo->prepare("UPDATE admin_users SET email = ? WHERE id = ?");
                                    $stmt->execute([$correct_email, $user['id']]);
                                    
                                    // 重新查询用户信息以获取更新后的邮箱
                                    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
                                    $stmt->execute([$user['id']]);
                                    $user = $stmt->fetch();
                                    
                                    error_log("🔧 登录时自动修复邮箱: <EMAIL> -> $correct_email");
                                }
                            }
                            // 🔧 自动邮箱修复逻辑 - 结束
                            
                            // 尝试发送验证码 - 使用修复版函数
                            if (sendVerificationEmailFixed($user['email'], $verification_code)) {
                                handleLoginRedirect('验证码已发送到您的邮箱，请查收（有效期20秒）', '', true);
                            } else {
                                // 邮件发送失败，但不阻止登录，记录错误并直接登录
                                error_log("验证码邮件发送失败，用户: " . $user['username'] . ", 邮箱: " . $user['email']);
                                $_SESSION['flash_warning'] = '邮件服务暂时不可用，已为您直接登录。建议在系统设置中检查邮箱配置。';
                                loginUser($user, $trust_device, $device_fingerprint);
                            }
                        } else {
                            // 验证邮箱验证码
                            $verification_time = $_SESSION['temp_verification_time'] ?? 0;
                            if ((time() - $verification_time) > 20) {
                                // 验证码过期，清理并重新登录
                                unset($_SESSION['temp_verification_code']);
                                unset($_SESSION['temp_verification_time']);
                                unset($_SESSION['temp_user_id']);
                                unset($_SESSION['temp_trust_device']);
                                unset($_SESSION['temp_device_fingerprint']);
                                unset($_SESSION['temp_verification_errors']);
                                handleLoginRedirect('', '验证码已过期，请重新登录');
                            } else {
                                // 检查验证码是否正确
                                if ($email_code === $_SESSION['temp_verification_code']) {
                                    // 验证码正确，登录成功
                                    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
                                    $stmt->execute([$_SESSION['temp_user_id']]);
                                    $user = $stmt->fetch();
                                    
                                    if ($user) {
                                        loginUser($user, $_SESSION['temp_trust_device'], $_SESSION['temp_device_fingerprint'], true);
                                    }
                                } else {
                                    // 验证码错误
                                    $error_count = ($_SESSION['temp_verification_errors'] ?? 0) + 1;
                                    $_SESSION['temp_verification_errors'] = $error_count;
                                    
                                    if ($error_count >= 2) {
                                        // 连续错误2次，清理数据
                                        unset($_SESSION['temp_verification_code']);
                                        unset($_SESSION['temp_verification_time']);
                                        unset($_SESSION['temp_user_id']);
                                        unset($_SESSION['temp_trust_device']);
                                        unset($_SESSION['temp_device_fingerprint']);
                                        unset($_SESSION['temp_verification_errors']);
                                        
                                        handleLoginRedirect('', '验证码连续错误，请重新登录');
                                    } else {
                                        handleLoginRedirect('', '验证码错误，还有' . (2 - $error_count) . '次机会', true);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                handleLoginRedirect('', '用户名或密码错误');
            }
        } catch (Exception $e) {
            error_log('登录错误: ' . $e->getMessage());
            handleLoginRedirect('', '登录失败，请重试');
        }
    }
}

// 登录用户函数
function loginUser($user, $trust_device, $device_fingerprint, $verified_by_email = false) {
    global $pdo;
    
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user_id'] = $user['id'];
    $_SESSION['admin_username'] = $user['username'];
    
    // 设置邮箱验证标志
    if ($verified_by_email) {
        $_SESSION['verified_by_email'] = true;
    }
    
    // 自动初始化令牌管理器
    define('SECURE_ACCESS', true);
    try {
        require_once '../api/auto_init_token_manager.php';
        $initResults = initializeTokenManager($pdo);
        error_log("用户登录后自动初始化令牌: " . json_encode($initResults));
    } catch (Exception $e) {
        error_log("自动初始化令牌失败: " . $e->getMessage());
    }
    
    // 🔧 关键修复：检查是否为信任设备，如果是则设置会话标志
    try {
        $stmt = $pdo->prepare("SELECT id FROM trusted_devices WHERE user_id = ? AND device_fingerprint = ? AND is_active = 1");
        $stmt->execute([$user['id'], $device_fingerprint]);
        $is_existing_trusted_device = $stmt->fetchColumn() !== false;
        
        if ($is_existing_trusted_device || $trust_device) {
            // 设置信任设备会话标志，避免被安全防护拦截
            $_SESSION['device_trusted_this_session'] = true;
            $_SESSION['trusted_device_fingerprint'] = $device_fingerprint;
            $_SESSION['trusted_device_verified_at'] = time();
        }
    } catch (Exception $e) {
        // 忽略错误，不影响登录流程
        error_log('检查信任设备状态失败: ' . $e->getMessage());
    }
    
    // 记录登录日志
    try {
        $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, ip_address, user_agent, login_time, device_fingerprint) VALUES (?, ?, ?, datetime('now'), ?)");
        $stmt->execute([$user['id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $device_fingerprint]);
    } catch (Exception $e) {
        // 如果login_logs表不存在，忽略错误
    }
    
    // 如果选择信任设备，添加到信任设备列表
    if ($trust_device) {
        try {
            $stmt = $pdo->prepare("INSERT INTO trusted_devices (user_id, device_fingerprint, device_name, ip_address, user_agent, created_at, is_active) VALUES (?, ?, ?, ?, ?, datetime('now'), 1) -- ON DUPLICATE KEY UPDATE is_active = 1, updated_at = datetime('now')");
            $device_name = getDeviceName();
            $stmt->execute([$user['id'], $device_fingerprint, $device_name, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);
        } catch (Exception $e) {
            // 如果trusted_devices表不存在，忽略错误
        }
    }
    
    // 发送登录通知（如果可能）
    if (!empty($user['email'])) {
        try {
            $security_monitor = getSecurityMonitor();
            if (function_exists('getDeviceFingerprint')) {
                $device_fingerprint = getDeviceFingerprint();
            } else {
                $device_fingerprint = 'unknown';
            }
            $is_trusted = $security_monitor->isTrustedDevice($user['id'], $device_fingerprint);

            $login_info = [
                'ip' => $_SERVER['REMOTE_ADDR'],
                'time' => date('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'device_name' => function_exists('getDeviceName') ? getDeviceName() : 'Unknown Device',
                'is_trusted' => $is_trusted
            ];

            $security_monitor->sendLoginNotification($user['email'], $login_info);
        } catch (Exception $e) {
            // 忽略通知错误
            error_log("登录通知发送失败: " . $e->getMessage());
        }
    }
    
    // 清理临时会话
    unset($_SESSION['temp_verification_code']);
    unset($_SESSION['temp_verification_time']);
    unset($_SESSION['temp_user_id']);
    unset($_SESSION['temp_trust_device']);
    unset($_SESSION['temp_device_fingerprint']);
    unset($_SESSION['temp_verification_errors']);
    
    header('Location: index.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 管理员登录</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        /* 动态背景粒子 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            animation: particleFloat 8s infinite linear;
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5), 0 0 16px rgba(255, 255, 255, 0.2);
        }
        
        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        /* 主容器 */
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 420px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
            animation: slideIn 0.8s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 157, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 10px 30px rgba(255, 107, 157, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(255, 107, 157, 0.5);
            }
        }
        
        .login-title {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }
        
        /* 系统状态提示 */
        .system-status {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            line-height: 1.5;
        }
        
        .system-status.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }
        
        .system-status i {
            margin-right: 8px;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-input {
            width: 100%;
            padding: 15px 20px 15px 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .form-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
        }
        
        /* 复选框样式 */
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .custom-checkbox {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }
        
        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .checkmark {
            height: 20px;
            width: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin-right: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .custom-checkbox input:checked ~ .checkmark {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-color: #ff6b9d;
        }
        
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }
        
        .checkbox-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }
        
        /* 按钮样式 */
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(255, 107, 157, 0.4);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .login-btn:hover::before {
            left: 100%;
        }
        
        /* 消息提示 */
        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #28a745;
        }
        
        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        
        .message.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }
        
        /* 邮箱验证区域 */
        .email-verify-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .verify-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .verify-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        /* 提示信息 */
        .login-note {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            line-height: 1.5;
        }
        
        .login-note i {
            color: #4ecdc4;
            margin-right: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .login-title {
                font-size: 20px;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>
    
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                🌸
            </div>
            <h1 class="login-title">小梅花AI客服系统</h1>
            <p class="login-subtitle">管理员控制面板</p>
        </div>
        
        <?php if (!$system_config['fully_configured']): ?>
            <div class="system-status warning">
                <i class="fas fa-info-circle"></i>
                <?php if (!$system_config['smtp_configured']): ?>
                    系统检测到SMTP邮箱服务尚未配置，登录后请在系统设置中完成邮箱配置以启用完整的安全功能。
                <?php elseif (!$system_config['email_configured']): ?>
                    建议配置管理员邮箱地址以获得更好的安全保护和系统通知。
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="message error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="message success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($warning_message): ?>
            <div class="message warning">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($warning_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($show_email_verify): ?>
            <div class="email-verify-section">
                <div class="verify-title">
                    <i class="fas fa-envelope"></i>
                    邮箱验证
                </div>
                <div class="verify-description">
                    验证码已发送到您的邮箱，请查收并输入6位验证码
                </div>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <i class="fas fa-user form-icon"></i>
                <input type="text" name="username" class="form-input" placeholder="请输入用户名" 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
            </div>
            
            <div class="form-group">
                <i class="fas fa-lock form-icon"></i>
                <input type="password" name="password" class="form-input" placeholder="请输入密码" required>
            </div>
            
            <?php if ($show_email_verify): ?>
                <div class="form-group">
                    <i class="fas fa-key form-icon"></i>
                    <input type="text" name="email_code" class="form-input" placeholder="请输入6位验证码" 
                           maxlength="6" pattern="[0-9]{6}" required>
                </div>
            <?php endif; ?>
            
            <?php if ($system_config['fully_configured']): ?>
                <div class="checkbox-group">
                    <label class="custom-checkbox">
                        <input type="checkbox" name="trust_device" value="1">
                        <span class="checkmark"></span>
                        <span class="checkbox-label">信任此设备30天</span>
                    </label>
                </div>
            <?php endif; ?>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                <?php echo $show_email_verify ? '验证并登录' : '立即登录'; ?>
            </button>
        </form>
    </div>
    
    <script>
        // 防止表单重新提交弹窗的解决方案
        (function() {
            'use strict';

            // 防止页面刷新时出现表单重新提交的提示
            function preventFormResubmissionDialog() {
                // 使用 history.replaceState 清除 POST 历史记录
                if (window.history.replaceState) {
                    window.history.replaceState(null, null, window.location.pathname + window.location.search);
                }
            }

            // 表单提交后的处理
            function handleLoginFormSubmission() {
                const loginForm = document.querySelector('form');

                if (loginForm && loginForm.method.toLowerCase() === 'post') {
                    loginForm.addEventListener('submit', function(e) {
                        // 标记表单已提交
                        sessionStorage.setItem('login_form_submitted', 'true');

                        // 延迟执行，确保表单提交完成后再清除历史记录
                        setTimeout(() => {
                            preventFormResubmissionDialog();
                        }, 100);
                    });
                }
            }

            // 页面加载时执行
            function initializeLoginFormProtection() {
                // 立即清除可能的POST状态
                preventFormResubmissionDialog();

                // 设置表单提交监听器
                handleLoginFormSubmission();

                // 清理session标记
                if (sessionStorage.getItem('login_form_submitted')) {
                    sessionStorage.removeItem('login_form_submitted');
                }
            }

            // 页面可见性变化时也执行清理
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    preventFormResubmissionDialog();
                }
            });

            // 浏览器前进后退时执行清理
            window.addEventListener('popstate', function() {
                preventFormResubmissionDialog();
            });

            // DOM加载完成后初始化
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeLoginFormProtection);
            } else {
                initializeLoginFormProtection();
            }

            // 页面完全加载后再次执行
            window.addEventListener('load', function() {
                setTimeout(preventFormResubmissionDialog, 500);
            });
        })();

        // 创建动态背景粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 60;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置
                particle.style.left = Math.random() * 100 + '%';
                
                // 随机动画延迟和持续时间
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
                
                // 随机大小
                const size = Math.random() * 3 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // 随机透明度
                const opacity = Math.random() * 0.5 + 0.3;
                particle.style.background = `rgba(255, 255, 255, ${opacity})`;
                particle.style.boxShadow = `0 0 ${size * 2}px rgba(255, 255, 255, ${opacity * 0.5})`;
                
                particlesContainer.appendChild(particle);
            }
            
            // 定期重新生成粒子
            setTimeout(createParticles, 12000);
        }
        
        // 页面加载完成后创建粒子
        document.addEventListener('DOMContentLoaded', createParticles);
    </script>
</body>
</html> 
<?php
/**
 * API网关
 * 
 * 作为API入口点，处理请求验证、令牌生成和响应
 * 使用环境变量获取数据库凭证，即使数据库密码变更，API功能也不受影响
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入API基础类
require_once 'api_base.php';

// 创建API实例
$api = new ApiBase();

// 定义路由和处理函数
$routes = [
    'auth/handshake' => 'handleHandshake',
    'auth/refresh' => 'handleRefreshToken',
    'verify/key' => 'handleVerifyKey',
    'heartbeat' => 'handleHeartbeat',
];

// 获取请求路径
$request_path = isset($_GET['path']) ? $_GET['path'] : '';
$start_time = microtime(true);

// 获取JSON请求数据
$request_data = json_decode(file_get_contents('php://input'), true) ?: [];

// 合并GET和POST参数
$params = array_merge($_GET, $_POST, $request_data);

// 基本请求信息
$ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$client_id = $params['client_id'] ?? ($params['device_id'] ?? null);
$api_key = $params['api_key'] ?? null;

// 从授权头获取JWT令牌
$jwt_token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    if (preg_match('/Bearer\s+(.*)$/i', $_SERVER['HTTP_AUTHORIZATION'], $matches)) {
        $jwt_token = $matches[1];
    }
}

// 处理请求
try {
    // 不需要认证的路由
    $public_routes = ['auth/handshake', 'verify/key'];
    
    if (in_array($request_path, $public_routes)) {
        // 公开接口，直接处理
        if (isset($routes[$request_path])) {
            $response = call_user_func($routes[$request_path], $api, $params);
        } else {
            $response = ['success' => false, 'error' => 'Invalid route', 'code' => 404];
        }
    } else {
        // 需要认证的接口
        if ($jwt_token) {
            // 验证JWT令牌
            $token_data = $api->validateJwtToken($jwt_token);
            
            if ($token_data) {
                // 令牌有效，处理请求
                if (isset($routes[$request_path])) {
                    $params['token_data'] = $token_data;
                    $response = call_user_func($routes[$request_path], $api, $params);
                } else {
                    $response = ['success' => false, 'error' => 'Invalid route', 'code' => 404];
                }
            } else {
                // 令牌无效
                $response = ['success' => false, 'error' => 'Invalid or expired token', 'code' => 401];
            }
        } else {
            // 缺少认证令牌
            $response = ['success' => false, 'error' => 'Authentication required', 'code' => 401];
        }
    }
} catch (Exception $e) {
    // 错误处理
    $response = ['success' => false, 'error' => $e->getMessage(), 'code' => 500];
}

// 设置HTTP状态码
$status_code = $response['code'] ?? 200;
http_response_code($status_code);

// 从响应中移除状态码
if (isset($response['code'])) {
    unset($response['code']);
}

// 添加时间戳和请求ID
$response['timestamp'] = date('Y-m-d H:i:s');
$response['request_id'] = uniqid('req_');

// 记录API访问日志
if ($api_key) {
    $key_data = $api->validateApiKey($api_key);
    if ($key_data) {
        $key_id = $key_data['id'];
        $response_time = microtime(true) - $start_time;
        $api->logApiAccess($key_id, $client_id, $ip_address, $_SERVER['REQUEST_METHOD'], $request_path, $status_code, $response_time);
    }
}

// 输出JSON响应
echo json_encode($response);
exit;

/**
 * 处理握手请求
 * 
 * @param ApiBase $api API实例
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handleHandshake($api, $params) {
    // 验证必要参数
    if (empty($params['api_key']) || empty($params['client_id'])) {
        return ['success' => false, 'error' => 'Missing required parameters', 'code' => 400];
    }
    
    // 验证API密钥
    $key_data = $api->validateApiKey($params['api_key']);
    if (!$key_data) {
        return ['success' => false, 'error' => 'Invalid API key', 'code' => 401];
    }
    
    // 检查设备指纹
    $device_fingerprint = $params['device_fingerprint'] ?? null;
    $client_id = $params['client_id'];
    $key_id = $key_data['id'];
    
    // 获取客户端风险评分
    $risk_score = $api->getClientRiskScore($key_id, $client_id);
    
    // 如果风险评分过高，拒绝请求
    if ($risk_score >= 50) {
        return [
            'success' => false, 
            'error' => 'This client has been locked due to suspicious activity', 
            'code' => 403,
            'risk_score' => $risk_score
        ];
    } elseif ($risk_score >= 25) {
        // 冷却状态 - 仍允许连接但发出警告
        $warning = 'This client is in cooling down state due to suspicious activity';
    } else {
        $warning = null;
    }
    
    // 生成JWT令牌
    $jwt_payload = [
        'key_id' => $key_id,
        'client_id' => $client_id,
        'device' => $device_fingerprint
    ];
    
    // 两小时有效期的会话令牌
    $session_token = $api->generateJwtToken($jwt_payload, 2);
    
    // 七天有效期的刷新令牌
    $refresh_token = $api->generateJwtToken([
        'key_id' => $key_id,
        'client_id' => $client_id,
        'type' => 'refresh'
    ], 24 * 7);
    
    // 成功响应
    $response = [
        'success' => true,
        'session_token' => $session_token,
        'refresh_token' => $refresh_token,
        'expires_in' => 7200,  // 2小时，以秒为单位
    ];
    
    // 如果有警告，添加到响应中
    if ($warning) {
        $response['warning'] = $warning;
        $response['risk_score'] = $risk_score;
    }
    
    return $response;
}

/**
 * 处理令牌刷新
 * 
 * @param ApiBase $api API实例
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handleRefreshToken($api, $params) {
    // 验证刷新令牌
    if (empty($params['refresh_token'])) {
        return ['success' => false, 'error' => 'Missing refresh token', 'code' => 400];
    }
    
    $refresh_token_data = $api->validateJwtToken($params['refresh_token']);
    
    if (!$refresh_token_data || !isset($refresh_token_data['type']) || $refresh_token_data['type'] !== 'refresh') {
        return ['success' => false, 'error' => 'Invalid refresh token', 'code' => 401];
    }
    
    // 从刷新令牌中提取数据
    $key_id = $refresh_token_data['key_id'];
    $client_id = $refresh_token_data['client_id'];
    
    // 生成新的会话令牌
    $jwt_payload = [
        'key_id' => $key_id,
        'client_id' => $client_id
    ];
    
    // 两小时有效期的会话令牌
    $session_token = $api->generateJwtToken($jwt_payload, 2);
    
    // 成功响应
    return [
        'success' => true,
        'session_token' => $session_token,
        'expires_in' => 7200,  // 2小时，以秒为单位
    ];
}

/**
 * 验证API密钥
 * 
 * @param ApiBase $api API实例
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handleVerifyKey($api, $params) {
    // 验证API密钥
    if (empty($params['api_key'])) {
        return ['success' => false, 'error' => 'Missing API key', 'code' => 400];
    }
    
    $key_data = $api->validateApiKey($params['api_key']);
    
    if ($key_data) {
        // 安全起见，不返回完整的密钥数据
        return [
            'success' => true,
            'key_info' => [
                'id' => $key_data['id'],
                'name' => $key_data['key_name'],
                'is_active' => (bool)$key_data['is_active'],
            ]
        ];
    } else {
        return ['success' => false, 'error' => 'Invalid API key', 'code' => 401];
    }
}

/**
 * 处理心跳请求
 * 
 * @param ApiBase $api API实例
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handleHeartbeat($api, $params) {
    // 从令牌数据中提取信息
    $token_data = $params['token_data'];
    $key_id = $token_data['key_id'];
    $client_id = $token_data['client_id'];
    
    // 获取客户端风险评分
    $risk_score = $api->getClientRiskScore($key_id, $client_id);
    
    return [
        'success' => true,
        'server_time' => date('Y-m-d H:i:s'),
        'status' => $risk_score >= 50 ? 'locked' : ($risk_score >= 25 ? 'cooling' : 'active'),
        'risk_score' => $risk_score
    ];
} 
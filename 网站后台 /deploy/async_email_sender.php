<?php
// 异步邮件发送脚本 - 处理登录提醒等邮件发送任务
// 避免邮件发送阻塞用户登录流程

set_time_limit(30); // 设置30秒超时
ignore_user_abort(true); // 忽略用户中断

require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查是否通过命令行或内部调用
$is_internal_call = (php_sapi_name() === 'cli') || 
                   (isset($_SERVER['HTTP_X_INTERNAL_CALL']) && $_SERVER['HTTP_X_INTERNAL_CALL'] === 'true') ||
                   (isset($_POST['internal_token']) && $_POST['internal_token'] === 'xiaomeihua_internal_2024');

if (!$is_internal_call) {
    http_response_code(403);
    exit('Access denied');
}

// 处理登录提醒邮件
function processLoginNotification($data) {
    try {
        $settings = getSystemSettings();
        
        // 检查是否启用登录提醒
        if (($settings['login_notification_enabled'] ?? '0') !== '1') {
            return ['success' => true, 'message' => '登录提醒未启用'];
        }
        
        // 检查SMTP配置
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整'];
        }
        
        $result = sendLoginNotification($data['email'], $data['ip'], $data['time']);
        
        return [
            'success' => $result,
            'message' => $result ? '登录提醒发送成功' : '登录提醒发送失败',
            'email' => $data['email'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log('异步登录提醒发送失败: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => '发送异常: ' . $e->getMessage()
        ];
    }
}

// 处理验证码邮件（如果需要异步发送）
function processVerificationEmail($data) {
    try {
        $result = sendVerificationEmail($data['email'], $data['code']);
        
        return [
            'success' => $result,
            'message' => $result ? '验证码发送成功' : '验证码发送失败',
            'email' => $data['email'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log('异步验证码发送失败: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => '发送异常: ' . $e->getMessage()
        ];
    }
}

// 主处理逻辑
$response = ['success' => false, 'message' => '未知错误'];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['type'])) {
            $response = ['success' => false, 'message' => '无效的请求数据'];
        } else {
            switch ($input['type']) {
                case 'login_notification':
                    $response = processLoginNotification($input['data']);
                    break;
                    
                case 'verification_email':
                    $response = processVerificationEmail($input['data']);
                    break;
                    
                default:
                    $response = ['success' => false, 'message' => '未知的邮件类型'];
            }
        }
    } elseif (php_sapi_name() === 'cli') {
        // 命令行模式 - 处理临时文件
        $temp_dir = sys_get_temp_dir();
        $pattern = $temp_dir . '/login_notification_*.json';
        $files = glob($pattern);
        
        $processed_count = 0;
        $success_count = 0;
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $data = json_decode(file_get_contents($file), true);
                if ($data) {
                    $result = processLoginNotification($data);
                    if ($result['success']) {
                        $success_count++;
                    }
                    $processed_count++;
                    error_log("处理登录提醒: " . json_encode($result));
                }
                unlink($file);
            }
        }
        
        $response = [
            'success' => true,
            'message' => "处理完成: {$processed_count} 个任务，{$success_count} 个成功"
        ];
        
        if (php_sapi_name() === 'cli') {
            echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        $response = ['success' => false, 'message' => '不支持的调用方式'];
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => '处理异常: ' . $e->getMessage()
    ];
    error_log('异步邮件发送脚本异常: ' . $e->getMessage());
}

// 输出响应
if (php_sapi_name() !== 'cli') {
    header('Content-Type: application/json');
    echo json_encode($response);
}

// 清理过期的临时文件（超过5分钟的文件）
function cleanupTempFiles() {
    $temp_dir = sys_get_temp_dir();
    $pattern = $temp_dir . '/login_notification_*.json';
    $files = glob($pattern);
    $now = time();
    
    foreach ($files as $file) {
        if (file_exists($file) && ($now - filemtime($file)) > 300) { // 5分钟
            unlink($file);
        }
    }
}

// 执行清理
cleanupTempFiles();
?> 
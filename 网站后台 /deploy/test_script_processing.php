<?php
/**
 * 测试脚本处理功能 - 验证头部信息保留功能
 */

// 直接定义脚本处理函数进行测试
function processUserScript($code) {
    $result = [
        'name' => '',
        'version' => '1.0.0',
        'code' => $code  // 保留完整的原始代码
    ];

    // 检查是否包含userscript头部
    if (strpos($code, '// ==UserScript==') !== false) {
        // 提取脚本名称
        if (preg_match('/@name\s+(.+)/i', $code, $matches)) {
            $result['name'] = trim($matches[1]);
        }

        // 提取版本号
        if (preg_match('/@version\s+(.+)/i', $code, $matches)) {
            $result['version'] = trim($matches[1]);
        }

        // 【优化】不再自动清理头部信息，保留完整代码
        $result['code'] = $code;  // 保持原始代码不变
    }

    return $result;
}

echo "=== 脚本处理功能测试 ===\n\n";

// 测试用的完整 UserScript 代码
$testScript = '// ==UserScript==
// @name         小梅花AI智能助手
// @namespace    http://xiaomeihuakefu.cn/
// @version      2.1.0
// @description  智能客服助手，提供AI对话和自动回复功能
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// ==/UserScript==

(function() {
    "use strict";
    
    console.log("小梅花AI智能助手已加载");
    
    // 初始化函数
    function init() {
        console.log("初始化AI助手...");
        createFloatingIcon();
        setupEventListeners();
    }
    
    // 创建浮动图标
    function createFloatingIcon() {
        const icon = document.createElement("div");
        icon.id = "ai-assistant-icon";
        icon.innerHTML = "🤖";
        icon.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #007bff;
            border-radius: 50%;
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        `;
        document.body.appendChild(icon);
    }
    
    // 设置事件监听器
    function setupEventListeners() {
        document.addEventListener("DOMContentLoaded", init);
    }
    
    // 页面加载完成后初始化
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", init);
    } else {
        init();
    }
    
})();';

echo "1. 测试原始脚本内容：\n";
echo "脚本长度：" . strlen($testScript) . " 字符\n";
echo "包含 UserScript 头部：" . (strpos($testScript, '// ==UserScript==') !== false ? '是' : '否') . "\n\n";

echo "2. 测试脚本处理函数：\n";
$result = processUserScript($testScript);

echo "处理结果：\n";
echo "- 提取的脚本名称：" . $result['name'] . "\n";
echo "- 提取的版本号：" . $result['version'] . "\n";
echo "- 处理后代码长度：" . strlen($result['code']) . " 字符\n";
echo "- 是否保留头部信息：" . (strpos($result['code'], '// ==UserScript==') !== false ? '是' : '否') . "\n";
echo "- 代码是否完整：" . ($result['code'] === $testScript ? '是' : '否') . "\n\n";

echo "3. 验证结果：\n";
if ($result['code'] === $testScript) {
    echo "✅ 测试通过：脚本代码完整保留，包括 UserScript 头部信息\n";
} else {
    echo "❌ 测试失败：脚本代码被修改\n";
    echo "原始长度：" . strlen($testScript) . "\n";
    echo "处理后长度：" . strlen($result['code']) . "\n";
}

if ($result['name'] === '小梅花AI智能助手') {
    echo "✅ 测试通过：正确提取脚本名称\n";
} else {
    echo "❌ 测试失败：脚本名称提取错误\n";
}

if ($result['version'] === '2.1.0') {
    echo "✅ 测试通过：正确提取版本号\n";
} else {
    echo "❌ 测试失败：版本号提取错误\n";
}

echo "\n=== 测试完成 ===\n";
?>

<?php
/**
 * 一键初始化安全系统
 * 版本: v2.0.0
 * 功能: 自动生成API密钥、JWT令牌并替换所有敏感信息
 */

// 设置执行时间限制
set_time_limit(600); // 10分钟

// 定义安全访问常量
define('API_SECURITY_INIT', true);

require_once 'config.php';
require_once 'token_manager.php';
require_once 'sensitive_fixer.php';

class SecurityInitializer {
    private $tokenManager;
    private $sensitiveFixer;
    private $config;
    
    public function __construct() {
        $this->tokenManager = new TokenManager();
        $this->sensitiveFixer = new SensitiveFixer();
        $this->config = SecureApiConfig::getInstance();
    }
    
    /**
     * 一键初始化安全系统
     */
    public function initialize() {
        echo "🔒 开始初始化安全系统...\n\n";
        
        try {
            // 1. 生成初始API密钥
            echo "📋 步骤 1/6: 生成API密钥...\n";
            $apiKey = $this->generateInitialApiKey();
            echo "✅ API密钥生成成功: " . substr($apiKey['key_value'], 0, 16) . "...\n\n";
            
            // 2. 生成JWT密钥
            echo "🔐 步骤 2/6: 生成JWT密钥...\n";
            $jwtSecret = $this->generateInitialJwtSecret();
            echo "✅ JWT密钥生成成功\n\n";
            
            // 3. 生成安全配置文件
            echo "📄 步骤 3/6: 生成安全配置文件...\n";
            $configFile = $this->sensitiveFixer->generateSecureConfigFile();
            echo "✅ 配置文件生成成功: " . $configFile . "\n\n";
            
            // 4. 扫描敏感信息
            echo "🔍 步骤 4/6: 扫描敏感信息...\n";
            $scanResults = $this->scanAndDisplaySensitiveInfo();
            echo "✅ 扫描完成，发现 " . count($scanResults) . " 个敏感信息\n\n";
            
            // 5. 自动修复敏感信息
            echo "🛠️ 步骤 5/6: 自动修复敏感信息...\n";
            $fixResults = $this->autoFixAllSensitive($scanResults);
            echo "✅ 修复完成，成功修复 " . count($fixResults['fixed']) . " 个敏感信息\n\n";
            
            // 6. 更新文件引用
            echo "🔗 步骤 6/6: 更新文件引用...\n";
            $this->sensitiveFixer->updateFileReferences();
            echo "✅ 文件引用更新完成\n\n";
            
            // 生成最终报告
            $this->generateInitializationReport($apiKey, $jwtSecret, $scanResults, $fixResults);
            
            echo "🎉 安全系统初始化完成！\n";
            echo "📊 查看详细报告请访问后台 API接口配置 页面\n\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 初始化失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 生成初始API密钥
     */
    private function generateInitialApiKey() {
        return $this->tokenManager->generateApiKey(
            'Primary API Key',
            true,  // 启用自动刷新
            86400  // 24小时刷新间隔
        );
    }
    
    /**
     * 生成初始JWT密钥
     */
    private function generateInitialJwtSecret() {
        return $this->tokenManager->generateJwtSecret(
            true,   // 启用自动刷新
            604800  // 7天刷新间隔
        );
    }
    
    /**
     * 扫描并显示敏感信息
     */
    private function scanAndDisplaySensitiveInfo() {
        $scanResults = $this->tokenManager->scanSensitiveInfo();
        
        if (empty($scanResults)) {
            echo "   ✅ 未发现敏感信息\n";
            return $scanResults;
        }
        
        $riskCounts = [
            'high' => 0,
            'medium' => 0,
            'low' => 0
        ];
        
        foreach ($scanResults as $result) {
            $riskCounts[$result['risk_level']]++;
            $riskIcon = $this->getRiskIcon($result['risk_level']);
            echo "   {$riskIcon} {$result['file']}:{$result['line']} - {$result['type']}\n";
        }
        
        echo "   📊 风险统计: 高风险({$riskCounts['high']}) 中风险({$riskCounts['medium']}) 低风险({$riskCounts['low']})\n";
        
        return $scanResults;
    }
    
    /**
     * 自动修复所有敏感信息
     */
    private function autoFixAllSensitive($scanResults) {
        if (empty($scanResults)) {
            return ['fixed' => [], 'failed' => []];
        }
        
        $results = $this->sensitiveFixer->batchFixAll();
        
        foreach ($results['fixed'] as $fixed) {
            echo "   ✅ 修复: {$fixed['file']}:{$fixed['line']}\n";
        }
        
        foreach ($results['failed'] as $failed) {
            echo "   ❌ 失败: {$failed['result']['file']}:{$failed['result']['line']} - {$failed['error']}\n";
        }
        
        return $results;
    }
    
    /**
     * 获取风险图标
     */
    private function getRiskIcon($riskLevel) {
        switch ($riskLevel) {
            case 'high':
                return '🔴';
            case 'medium':
                return '🟡';
            case 'low':
                return '🟢';
            default:
                return '⚪';
        }
    }
    
    /**
     * 生成初始化报告
     */
    private function generateInitializationReport($apiKey, $jwtSecret, $scanResults, $fixResults) {
        $report = [
            'initialization_time' => date('Y-m-d H:i:s'),
            'api_key_generated' => [
                'id' => $apiKey['api_key_id'],
                'token_id' => $apiKey['token_id'],
                'expires_at' => $apiKey['expires_at']
            ],
            'jwt_secret_generated' => [
                'token_id' => $jwtSecret['token_id'],
                'expires_at' => $jwtSecret['expires_at']
            ],
            'sensitive_scan' => [
                'total_found' => count($scanResults),
                'high_risk' => count(array_filter($scanResults, function($r) { return $r['risk_level'] === 'high'; })),
                'medium_risk' => count(array_filter($scanResults, function($r) { return $r['risk_level'] === 'medium'; })),
                'low_risk' => count(array_filter($scanResults, function($r) { return $r['risk_level'] === 'low'; }))
            ],
            'fix_results' => [
                'fixed_count' => count($fixResults['fixed']),
                'failed_count' => count($fixResults['failed']),
                'success_rate' => count($scanResults) > 0 ? round(count($fixResults['fixed']) / count($scanResults) * 100, 2) : 100
            ],
            'status' => 'completed'
        ];
        
        // 保存报告到数据库
        $this->saveInitializationReport($report);
        
        // 显示摘要
        echo "📋 初始化摘要:\n";
        echo "   🔑 API密钥ID: {$apiKey['api_key_id']}\n";
        echo "   🔐 JWT令牌ID: {$jwtSecret['token_id']}\n";
        echo "   🔍 敏感信息: {$report['sensitive_scan']['total_found']} 个\n";
        echo "   🛠️ 修复成功: {$report['fix_results']['fixed_count']} 个\n";
        echo "   📈 成功率: {$report['fix_results']['success_rate']}%\n";
    }
    
    /**
     * 保存初始化报告
     */
    private function saveInitializationReport($report) {
        $pdo = $this->config->getDatabaseConnection();
        
        // 创建初始化报告表
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS security_initialization_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                report_data JSON NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_created (created_at)
            )
        ");
        
        $stmt = $pdo->prepare("INSERT INTO security_initialization_log (report_data) VALUES (?)");
        $stmt->execute([json_encode($report)]);
    }
    
    /**
     * 创建定时任务
     */
    public function setupCronJob() {
        echo "⏰ 设置定时任务...\n";
        
        $cronScript = dirname(__FILE__) . '/auto_refresh_cron.php';
        $cronCommand = "*/30 * * * * /usr/bin/php {$cronScript} >> /dev/null 2>&1";
        
        echo "   添加以下内容到 crontab:\n";
        echo "   {$cronCommand}\n";
        echo "   或者运行: echo '{$cronCommand}' | crontab -\n\n";
    }
    
    /**
     * 显示使用指南
     */
    public function showUsageGuide() {
        echo "📖 使用指南:\n\n";
        
        echo "1. 🔄 自动刷新设置:\n";
        echo "   - API密钥每24小时自动刷新\n";
        echo "   - JWT密钥每7天自动刷新\n";
        echo "   - 系统会自动同步到所有文件\n\n";
        
        echo "2. 🔍 敏感信息监控:\n";
        echo "   - 系统每30分钟自动扫描敏感信息\n";
        echo "   - 高风险敏感信息会自动修复\n";
        echo "   - 修复日志保存在数据库中\n\n";
        
        echo "3. 🛡️ 安全配置:\n";
        echo "   - 所有敏感信息已加密存储\n";
        echo "   - API调用使用JWT认证\n";
        echo "   - 请求限流和CORS保护\n\n";
        
        echo "4. 📊 监控面板:\n";
        echo "   - 访问后台 > API接口配置 查看状态\n";
        echo "   - 实时监控令牌状态和敏感信息\n";
        echo "   - 查看同步日志和修复记录\n\n";
        
        echo "5. ⚠️ 重要提醒:\n";
        echo "   - 请定期检查后台API配置页面\n";
        echo "   - 确保定时任务正常运行\n";
        echo "   - 如有异常请查看日志文件\n\n";
    }
}

// 执行初始化
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // 命令行执行
    $initializer = new SecurityInitializer();
    
    echo "🔐 小美花客服系统 - 安全初始化工具 v2.0.0\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $success = $initializer->initialize();
    
    if ($success) {
        echo "\n" . str_repeat("=", 52) . "\n";
        $initializer->setupCronJob();
        $initializer->showUsageGuide();
    }
    
} else {
    // Web访问
    header('Content-Type: text/plain; charset=utf-8');
    
    echo "🔐 小美花客服系统 - 安全初始化\n";
    echo "=" . str_repeat("=", 40) . "\n\n";
    
    $initializer = new SecurityInitializer();
    $success = $initializer->initialize();
    
    if ($success) {
        echo "\n✅ 初始化完成！请查看后台API配置页面获取详细信息。\n";
    }
} 
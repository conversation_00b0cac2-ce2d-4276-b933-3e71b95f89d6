<?php
/**
 * Windows APP更新独立API接口
 * 专门为Windows用户提供更新检查和下载服务
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'platform' => 'windows',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'platform' => 'windows',
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接连接MySQL数据库
function getDatabase() {
    try {
        $pdo = new PDO(
            "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
            "xiaomeihuakefu_c",
            "7Da5F1Xx995cxYz8",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 10
            ]
        );
        
        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");
        
        return $pdo;
    } catch (Exception $e) {
        sendError('服务暂时不可用，请稍后重试', 503);
    }
}

// 记录访问日志
function logAccess($pdo, $action, $version = null, $userAgent = null, $extra_data = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO update_access_logs 
            (platform, action, client_version, user_agent, ip_address, extra_data, access_time) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            'windows',
            $action,
            $version,
            $userAgent ?: $_SERVER['HTTP_USER_AGENT'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $extra_data ? json_encode($extra_data) : null
        ]);
    } catch (Exception $e) {
        error_log('记录访问日志失败: ' . $e->getMessage());
    }
}

/**
 * 检查Windows更新
 */
function handleWindowsUpdateCheck($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // 记录访问日志
    logAccess($pdo, 'check_update', $currentVersion, $userAgent);
    
    try {
        // 获取最新发布的版本（必须有Windows下载链接）- 增强版本验证
        $stmt = $pdo->prepare("
            SELECT *, version, title, description,
                   exe_download_url, exe_backup_url
            FROM app_updates
            WHERE status = 'published'
            AND exe_download_url IS NOT NULL
            AND (platform = 'windows' OR platform = 'all')
            AND (version IS NOT NULL AND version != '' AND TRIM(version) != '')
            ORDER BY id DESC, created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $latestUpdate = $stmt->fetch();

        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '暂无Windows版本可用更新'
            ], '无可用更新');
        }

        // 验证版本号格式
        if (!isValidVersion($latestUpdate['version'])) {
            error_log("Windows API发现无效版本号: " . $latestUpdate['version']);
            logAccess($pdo, 'invalid_version', $currentVersion, $userAgent, [
                'invalid_version' => $latestUpdate['version']
            ]);
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '版本信息异常，请联系管理员'
            ], '版本信息异常');
        }
        
        // 增强版本比较逻辑
        $hasUpdate = compareVersionsRobust($latestUpdate['version'], $currentVersion);
        
        // 额外的安全检查：如果版本号完全相同，强制返回无更新
        if (normalizeVersion($latestUpdate['version']) === normalizeVersion($currentVersion)) {
            $hasUpdate = false;
            logAccess($pdo, 'same_version_detected', $currentVersion, $userAgent, [
                'latest_version' => $latestUpdate['version'],
                'normalized_current' => normalizeVersion($currentVersion),
                'normalized_latest' => normalizeVersion($latestUpdate['version'])
            ]);
        }
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version'],
            'version_comparison_result' => [
                'normalized_current' => normalizeVersion($currentVersion),
                'normalized_latest' => normalizeVersion($latestUpdate['version']),
                'comparison_method' => 'robust'
            ]
        ];
        
        if ($hasUpdate) {
            // 优先使用直链，备用链接作为fallback
            $downloadUrl = $latestUpdate['exe_download_url'] ?: $latestUpdate['exe_backup_url'];
            $backupUrl = $latestUpdate['exe_backup_url'];
            
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'],
                'description' => $latestUpdate['description'],
                'force_update' => (bool)$latestUpdate['force_update'],
                'download_url' => $downloadUrl,
                'backup_url' => $backupUrl,
                'file_size' => null, // 可以后续添加文件大小检测
                'created_at' => $latestUpdate['created_at'],
                'release_notes' => $latestUpdate['release_notes']
            ];
            
            // 记录发现更新日志
            logAccess($pdo, 'found_update', $currentVersion);
        }
        
        sendSuccess($response, $hasUpdate ? '发现Windows新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        error_log('Windows更新检查失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        error_log('SQL查询失败，检查数据库连接和字段名');
        sendError('检查更新失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取Windows下载链接
 */
function handleWindowsDownload($pdo) {
    $updateId = $_GET['update_id'] ?? null;
    $version = $_GET['version'] ?? null;
    
    if (!$updateId && !$version) {
        sendError('缺少更新ID或版本号参数');
    }
    
    try {
        // 根据ID或版本获取更新信息
        if ($updateId) {
            $stmt = $pdo->prepare("
                SELECT *, version, title, description,
                       exe_download_url, exe_backup_url
                FROM app_updates 
                WHERE id = ? AND status = 'published'
                AND exe_download_url IS NOT NULL
            ");
            $stmt->execute([$updateId]);
        } else {
            $stmt = $pdo->prepare("
                SELECT *, version, title, description,
                       exe_download_url, exe_backup_url
                FROM app_updates 
                WHERE version = ? AND status = 'published'
                AND exe_download_url IS NOT NULL
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$version]);
        }
        
        $update = $stmt->fetch();
        
        if (!$update) {
            sendError('Windows版本不存在或未发布', 404);
        }
        
        // 优先返回直链
        $downloadUrl = $update['exe_download_url'] ?: $update['exe_backup_url'];
        $backupUrl = $update['exe_backup_url'];
        
        if (!$downloadUrl) {
            sendError('Windows安装包下载链接不可用', 404);
        }
        
        // 更新下载统计
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$update['id']]);
        
        // 记录下载日志
        logAccess($pdo, 'download', $update['version']);
        
        // 如果请求直接下载，进行重定向
        if (isset($_GET['direct']) && $_GET['direct'] == '1') {
            header('Location: ' . $downloadUrl);
            exit;
        }
        
        sendSuccess([
            'download_url' => $downloadUrl,
            'backup_url' => $backupUrl,
            'version' => $update['version'],
            'title' => $update['title'],
            'file_name' => 'XiaoMeiHua-Windows-v' . $update['version'] . '.exe',
            'download_count' => $update['download_count'] + 1
        ], '获取Windows下载链接成功');
        
    } catch (Exception $e) {
        error_log('获取Windows下载链接失败: ' . $e->getMessage());
        sendError('获取下载链接失败，请稍后重试', 500);
    }
}

/**
 * 获取Windows版本列表
 */
function handleWindowsVersionList($pdo) {
    try {
        $limit = min(20, max(1, intval($_GET['limit'] ?? 5)));
        
        $stmt = $pdo->prepare("
            SELECT id, version, title, description, 
                   exe_download_url, exe_backup_url,
                   force_update, download_count, created_at
            FROM app_updates 
            WHERE status = 'published' 
            AND exe_download_url IS NOT NULL
            AND (platform = 'windows' OR platform = 'all')
            ORDER BY id DESC, created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $versions = $stmt->fetchAll();
        
        // 处理版本数据
        foreach ($versions as &$version) {
            $version['has_direct_link'] = !empty($version['exe_download_url']);
            $version['has_backup_link'] = !empty($version['exe_backup_url']);
            $version['download_url'] = $version['exe_download_url'] ?: $version['exe_backup_url'];
            
            // 移除敏感信息
            unset($version['exe_download_url']);
            unset($version['exe_backup_url']);
        }
        
        sendSuccess([
            'versions' => $versions,
            'total' => count($versions)
        ], '获取Windows版本列表成功');
        
    } catch (Exception $e) {
        error_log('获取Windows版本列表失败: ' . $e->getMessage());
        sendError('获取版本列表失败，请稍后重试', 500);
    }
}

// 获取数据库连接
$pdo = getDatabase();

// 创建访问日志表（如果不存在）
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS update_access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            platform ENUM('windows', 'macos_m1', 'macos_intel') NOT NULL,
            action VARCHAR(50) NOT NULL,
            client_version VARCHAR(50),
            user_agent TEXT,
            ip_address VARCHAR(45),
            extra_data TEXT,
            access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_platform_action (platform, action),
            INDEX idx_access_time (access_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 更新表结构
    updateLogTableStructure($pdo);
} catch (Exception $e) {
    error_log('创建访问日志表失败: ' . $e->getMessage());
}

// API路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? 'check';

switch ($method) {
    case 'GET':
        switch ($action) {
            case 'check':
                handleWindowsUpdateCheck($pdo);
                break;
            case 'download':
                handleWindowsDownload($pdo);
                break;
            case 'versions':
                handleWindowsVersionList($pdo);
                break;
            case 'info':
                // 返回API信息
                sendSuccess([
                    'api_name' => 'Windows APP更新API',
                    'version' => '1.0.0',
                    'platform' => 'windows',
                    'endpoints' => [
                        'check' => '检查更新 (?action=check&version=1.0.0)',
                        'download' => '获取下载链接 (?action=download&update_id=1 或 &version=1.0.1)',
                        'versions' => '获取版本列表 (?action=versions&limit=5)',
                        'info' => '获取API信息 (?action=info)'
                    ]
                ], 'Windows更新API就绪');
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('仅支持GET请求', 405);
}

/**
 * 强化版本比较函数 - 解决重复更新提示问题
 */
function compareVersionsRobust($version1, $version2) {
    // 标准化版本号
    $v1 = normalizeVersion($version1);
    $v2 = normalizeVersion($version2);
    
    // 如果版本号完全相同，返回false（无更新）
    if ($v1 === $v2) {
        return false;
    }
    
    // 使用PHP内置函数进行比较
    $result = version_compare($v1, $v2, '>');
    
    return $result;
}

/**
 * 标准化版本号格式
 */
function normalizeVersion($version) {
    if (empty($version)) {
        return '0.0.0';
    }
    
    // 移除可能的前缀（如 v1.0.0）
    $clean = preg_replace('/^v/', '', trim($version));
    
    // 确保版本号格式为 x.y.z
    $parts = explode('.', $clean);
    
    // 补齐缺失的部分
    while (count($parts) < 3) {
        $parts[] = '0';
    }
    
    // 确保每部分都是数字
    $parts = array_map(function($part) {
        return preg_replace('/[^0-9]/', '', $part) ?: '0';
    }, $parts);
    
    // 只取前三位
    $parts = array_slice($parts, 0, 3);

    return implode('.', $parts);
}

/**
 * 验证版本号格式
 */
function isValidVersion($version) {
    if (empty($version) || !is_string($version)) {
        return false;
    }

    // 移除可能的前缀
    $clean = preg_replace('/^v/', '', trim($version));

    // 检查是否符合 x.y.z 格式
    if (!preg_match('/^[0-9]+\.[0-9]+\.[0-9]+$/', $clean)) {
        return false;
    }

    // 检查每个部分是否为有效数字
    $parts = explode('.', $clean);
    foreach ($parts as $part) {
        if (!is_numeric($part) || intval($part) < 0) {
            return false;
        }
    }

    return true;
}

/**
 * 更新访问日志表结构
 */
function updateLogTableStructure($pdo) {
    try {
        // 检查是否需要添加extra_data列
        $result = $pdo->query("SHOW COLUMNS FROM update_access_logs LIKE 'extra_data'");
        if ($result->rowCount() === 0) {
            $pdo->exec("ALTER TABLE update_access_logs ADD COLUMN extra_data TEXT AFTER ip_address");
        }
    } catch (Exception $e) {
        error_log('更新日志表结构失败: ' . $e->getMessage());
    }
}

?>
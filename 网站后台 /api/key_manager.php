<?php
/**
 * API密钥管理器
 * 
 * 提供API密钥管理和自动更新功能
 */

// 严格的错误控制
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/api_key_manager.log');

// 设置执行时间限制
set_time_limit(30);

/**
 * API密钥管理类
 */
class ApiKeyManager {
    private $pdo;
    
    /**
     * 构造函数
     */
    public function __construct($pdo = null) {
        if ($pdo) {
            $this->pdo = $pdo;
        } else {
            $this->pdo = $this->getOptimizedDB();
        }
        
        // 初始化表结构
        $this->initTables();
    }
    
    /**
     * 获取优化的数据库连接
     */
    private function getOptimizedDB() {
        try {
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_SILENT,
                PDO::ATTR_TIMEOUT => 3,
                PDO::ATTR_PERSISTENT => false
            ];
            
            // 智能路径检测
            $possible_paths = [
                __DIR__ . '/../database.db',
                dirname(__DIR__) . '/database.db',
                $_SERVER['DOCUMENT_ROOT'] . '/database.db',
                'database.db',
                '/www/wwwroot/www.xiaomeihuakefu.cn/database.db'
            ];
            
            $db_path = null;
            foreach ($possible_paths as $path) {
                if (file_exists($path) && is_readable($path)) {
                    $db_path = $path;
                    break;
                }
            }
            
            if ($db_path) {
                $pdo = new PDO('sqlite:' . $db_path, null, null, $options);
                $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
                return $pdo;
            } else {
                throw new Exception("无法找到数据库文件");
            }
        } catch (Exception $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 初始化表结构
     */
    private function initTables() {
        if (!$this->pdo) {
            return false;
        }
        
        try {
            // 创建API密钥自动更新设置表
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS api_key_auto_update (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_id INTEGER NOT NULL,
                    token_type TEXT NOT NULL,
                    auto_update BOOLEAN DEFAULT 1,
                    update_interval INTEGER DEFAULT 604800,
                    last_updated DATETIME,
                    next_update DATETIME,
                    created_at DATETIME DEFAULT (datetime('now')),
                    updated_at DATETIME DEFAULT (datetime('now')),
                    FOREIGN KEY (token_id) REFERENCES token_management(id) ON DELETE CASCADE
                )
            ");
            
            // 创建索引
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_token_id ON api_key_auto_update (token_id)");
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_next_update ON api_key_auto_update (next_update)");
            
            return true;
        } catch (Exception $e) {
            error_log("初始化表结构失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 设置API密钥自动更新
     */
    public function setAutoUpdate($tokenId, $tokenType, $autoUpdate = true, $updateInterval = 604800) {
        if (!$this->pdo) {
            return ['success' => false, 'message' => '数据库连接失败'];
        }
        
        try {
            // 检查令牌是否存在
            $stmt = $this->pdo->prepare("SELECT id FROM token_management WHERE id = ? AND token_type = ?");
            $stmt->execute([$tokenId, $tokenType]);
            $token = $stmt->fetch();
            
            if (!$token) {
                return ['success' => false, 'message' => '找不到指定的令牌'];
            }
            
            // 计算下次更新时间
            $nextUpdate = $autoUpdate ? date('Y-m-d H:i:s', time() + $updateInterval) : null;
            
            // 检查是否已有设置
            $stmt = $this->pdo->prepare("SELECT id FROM api_key_auto_update WHERE token_id = ?");
            $stmt->execute([$tokenId]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // 更新现有设置
                $stmt = $this->pdo->prepare("
                    UPDATE api_key_auto_update 
                    SET auto_update = ?, 
                        update_interval = ?, 
                        next_update = ?,
                        updated_at = datetime('now')
                    WHERE token_id = ?
                ");
                $result = $stmt->execute([$autoUpdate ? 1 : 0, $updateInterval, $nextUpdate, $tokenId]);
            } else {
                // 创建新设置
                $stmt = $this->pdo->prepare("
                    INSERT INTO api_key_auto_update 
                    (token_id, token_type, auto_update, update_interval, next_update) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $result = $stmt->execute([$tokenId, $tokenType, $autoUpdate ? 1 : 0, $updateInterval, $nextUpdate]);
            }
            
            if ($result) {
                // 同时更新token_management表中的自动刷新设置
                $stmt = $this->pdo->prepare("
                    UPDATE token_management 
                    SET auto_refresh = ?, 
                        refresh_interval = ?, 
                        expires_at = ?,
                        updated_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$autoUpdate ? 1 : 0, $updateInterval, $nextUpdate, $tokenId]);
                
                return [
                    'success' => true, 
                    'message' => '自动更新设置已保存',
                    'data' => [
                        'token_id' => $tokenId,
                        'auto_update' => $autoUpdate,
                        'update_interval' => $updateInterval,
                        'next_update' => $nextUpdate
                    ]
                ];
            } else {
                return ['success' => false, 'message' => '保存设置失败'];
            }
        } catch (Exception $e) {
            error_log("设置自动更新失败: " . $e->getMessage());
            return ['success' => false, 'message' => '设置自动更新时发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 获取API密钥自动更新设置
     */
    public function getAutoUpdateSettings($tokenId = null) {
        if (!$this->pdo) {
            return ['success' => false, 'message' => '数据库连接失败'];
        }
        
        try {
            if ($tokenId) {
                // 获取特定令牌的设置
                $stmt = $this->pdo->prepare("
                    SELECT a.*, t.token_type, t.expires_at, t.auto_refresh
                    FROM api_key_auto_update a
                    JOIN token_management t ON a.token_id = t.id
                    WHERE a.token_id = ?
                ");
                $stmt->execute([$tokenId]);
                $settings = $stmt->fetch();
                
                if ($settings) {
                    return ['success' => true, 'data' => $settings];
                } else {
                    return ['success' => false, 'message' => '找不到指定令牌的自动更新设置'];
                }
            } else {
                // 获取所有设置
                $stmt = $this->pdo->prepare("
                    SELECT a.*, t.token_type, t.expires_at, t.auto_refresh
                    FROM api_key_auto_update a
                    JOIN token_management t ON a.token_id = t.id
                    ORDER BY a.next_update ASC
                ");
                $stmt->execute();
                $settings = $stmt->fetchAll();
                
                return ['success' => true, 'data' => $settings];
            }
        } catch (Exception $e) {
            error_log("获取自动更新设置失败: " . $e->getMessage());
            return ['success' => false, 'message' => '获取自动更新设置时发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 执行自动更新
     */
    public function runAutoUpdate() {
        if (!$this->pdo) {
            return ['success' => false, 'message' => '数据库连接失败'];
        }
        
        try {
            // 获取所有需要更新的令牌
            $stmt = $this->pdo->prepare("
                SELECT a.token_id, a.token_type, a.update_interval, t.token_value
                FROM api_key_auto_update a
                JOIN token_management t ON a.token_id = t.id
                WHERE a.auto_update = 1
                AND a.next_update <= datetime('now')
                AND t.is_active = 1
            ");
            $stmt->execute();
            $tokensToUpdate = $stmt->fetchAll();
            
            $updated = 0;
            $errors = 0;
            $details = [];
            
            foreach ($tokensToUpdate as $token) {
                try {
                    // 生成新的令牌值
                    $newTokenValue = $this->generateUniqueToken($token['token_type']);
                    
                    // 计算新的过期时间
                    $updateInterval = (int)$token['update_interval'];
                    $nextUpdate = date('Y-m-d H:i:s', time() + $updateInterval);
                    
                    // 更新令牌
                    $updateStmt = $this->pdo->prepare("
                        UPDATE token_management 
                        SET token_value = ?, 
                            expires_at = ?, 
                            updated_at = datetime('now') 
                        WHERE id = ?
                    ");
                    
                    $result = $updateStmt->execute([$newTokenValue, $nextUpdate, $token['token_id']]);
                    
                    if ($result) {
                        // 更新自动更新设置
                        $this->pdo->prepare("
                            UPDATE api_key_auto_update 
                            SET last_updated = datetime('now'),
                                next_update = ?,
                                updated_at = datetime('now')
                            WHERE token_id = ?
                        ")->execute([$nextUpdate, $token['token_id']]);
                        
                        $updated++;
                        $details[] = [
                            'token_id' => $token['token_id'],
                            'token_type' => $token['token_type'],
                            'status' => 'success',
                            'next_update' => $nextUpdate
                        ];
                    } else {
                        $errors++;
                        $details[] = [
                            'token_id' => $token['token_id'],
                            'token_type' => $token['token_type'],
                            'status' => 'error',
                            'message' => '更新失败'
                        ];
                    }
                } catch (Exception $e) {
                    $errors++;
                    $details[] = [
                        'token_id' => $token['token_id'],
                        'token_type' => $token['token_type'],
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            }
            
            return [
                'success' => true,
                'message' => "自动更新完成: 更新了 {$updated} 个令牌, {$errors} 个错误",
                'updated' => $updated,
                'errors' => $errors,
                'details' => $details
            ];
        } catch (Exception $e) {
            error_log("执行自动更新失败: " . $e->getMessage());
            return ['success' => false, 'message' => '执行自动更新时发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 生成唯一令牌
     */
    private function generateUniqueToken($type = 'api_key') {
        if ($type === 'api_key') {
            return bin2hex(random_bytes(32)); // 64字符长度
        } else {
            return bin2hex(random_bytes(64)); // 128字符长度
        }
    }
    
    /**
     * 获取可读的时间间隔文本
     */
    public static function getIntervalText($seconds) {
        if ($seconds < 3600) {
            return floor($seconds / 60) . '分钟';
        } elseif ($seconds < 86400) {
            return floor($seconds / 3600) . '小时';
        } elseif ($seconds < 2592000) {
            return floor($seconds / 86400) . '天';
        } else {
            return floor($seconds / 2592000) . '个月';
        }
    }
}

// 如果直接访问此文件，执行自动更新
if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
    // 创建API密钥管理器
    $manager = new ApiKeyManager();
    
    // 执行自动更新
    $result = $manager->runAutoUpdate();
    
    // 如果是通过命令行运行，输出结果
    if (php_sapi_name() === 'cli') {
        echo $result['success'] ? $result['message'] . "\n" : "自动更新API密钥失败: " . $result['message'] . "\n";
    }
    
    // 如果是通过HTTP请求运行，返回JSON响应
    if (!empty($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }
    
    // 如果是通过HTTP请求运行但不需要JSON响应，返回简单文本
    if (!empty($_SERVER['REQUEST_METHOD'])) {
        header('Content-Type: text/plain');
        echo $result['success'] ? $result['message'] : "自动更新API密钥失败: " . $result['message'];
    }
} 
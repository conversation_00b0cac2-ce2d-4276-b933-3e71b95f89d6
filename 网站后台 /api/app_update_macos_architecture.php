<?php
/**
 * APP更新API - macOS架构支持版本
 * 支持M芯片和intel版本的智能检测和下载
 * 
 * 功能：
 * 1. 自动检测用户的macOS架构（M1/M2 或 intel）
 * 2. 根据架构返回对应的下载链接
 * 3. 支持手动指定架构
 * 4. 统计不同架构的下载数据
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
require_once '../config/database.php';

// 工具函数
function sendSuccess($data = null, $message = '操作成功') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendError($message = '操作失败', $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'error_code' => $code,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function logError($message, $context = []) {
    error_log(date('Y-m-d H:i:s') . " [APP_UPDATE_MACOS] $message " . json_encode($context));
}

// 数据库连接
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    logError('数据库连接失败', ['error' => $e->getMessage()]);
    sendError('服务暂时不可用，请稍后重试', 503);
}

// macOS架构检测类
class MacOSArchitectureDetector {
    private $userAgent;
    private $clientHints;
    
    public function __construct($userAgent = null, $clientHints = []) {
        $this->userAgent = $userAgent ?: $_SERVER['HTTP_USER_AGENT'] ?? '';
        $this->clientHints = $clientHints;
    }
    
    /**
     * 检测macOS架构
     */
    public function detectArchitecture() {
        $architecture = 'unknown';
        $confidence = 0;
        $method = 'unknown';
        
        // 方法1: 基于User-Agent检测
        $uaResult = $this->detectFromUserAgent();
        if ($uaResult['architecture'] !== 'unknown') {
            $architecture = $uaResult['architecture'];
            $confidence = $uaResult['confidence'];
            $method = 'user_agent';
        }
        
        // 方法2: 基于Client Hints检测（如果可用）
        $chResult = $this->detectFromClientHints();
        if ($chResult['architecture'] !== 'unknown' && $chResult['confidence'] > $confidence) {
            $architecture = $chResult['architecture'];
            $confidence = $chResult['confidence'];
            $method = 'client_hints';
        }
        
        return [
            'architecture' => $architecture,
            'confidence' => $confidence,
            'method' => $method,
            'user_agent' => $this->userAgent
        ];
    }
    
    /**
     * 从User-Agent检测架构
     */
    private function detectFromUserAgent() {
        $ua = strtolower($this->userAgent);
        
        // M1/M2芯片特征
        if (strpos($ua, 'arm64') !== false || 
            strpos($ua, 'apple silicon') !== false ||
            strpos($ua, 'arm64_64') !== false) {
            return ['architecture' => 'm1', 'confidence' => 90];
        }
        
        // Intel芯片特征
        if (strpos($ua, 'intel') !== false || 
            strpos($ua, 'x86_64') !== false ||
            strpos($ua, 'x64') !== false) {
            return ['architecture' => 'intel', 'confidence' => 85];
        }
        
        // macOS版本推断（不太准确）
        if (preg_match('/mac os x (\d+)_(\d+)/', $ua, $matches)) {
            $major = intval($matches[1]);
            $minor = intval($matches[2]);
            
            // macOS 11.0+ 更可能是M1
            if ($major >= 11) {
                return ['architecture' => 'm1', 'confidence' => 60];
            } else {
                return ['architecture' => 'intel', 'confidence' => 70];
            }
        }
        
        return ['architecture' => 'unknown', 'confidence' => 0];
    }
    
    /**
     * 从Client Hints检测架构
     */
    private function detectFromClientHints() {
        // 这里可以扩展Client Hints检测逻辑
        // 目前大多数浏览器还不完全支持架构相关的Client Hints
        return ['architecture' => 'unknown', 'confidence' => 0];
    }
}

// 主要API处理函数

/**
 * 检查更新（支持架构检测）
 */
function handleCheckUpdateWithArchitecture($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $platform = strtolower($_GET['platform'] ?? 'all');
    $requestedArch = $_GET['architecture'] ?? null; // 手动指定架构
    
    try {
        // 获取最新发布的版本
        $stmt = $pdo->prepare("
            SELECT * FROM app_updates 
            WHERE status = 'published' AND (platform = ? OR platform = 'all')
            ORDER BY version DESC, created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$platform]);
        $latestUpdate = $stmt->fetch();
        
        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'message' => '暂无可用更新'
            ], '无可用更新');
        }
        
        // 比较版本号
        $hasUpdate = version_compare($latestUpdate['version'], $currentVersion, '>');
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version']
        ];
        
        if ($hasUpdate) {
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'],
                'description' => $latestUpdate['description'],
                'force_update' => (bool)$latestUpdate['force_update'],
                'created_at' => $latestUpdate['created_at']
            ];
            
            // 处理下载链接（支持架构检测）
            $downloadUrls = [];
            
            // Windows链接
            if ($latestUpdate['exe_download_url']) {
                $downloadUrls['windows'] = $latestUpdate['exe_download_url'];
            }
            
            // macOS链接处理
            if ($platform === 'macos' || $platform === 'all') {
                $macosUrls = $this->getMacOSDownloadUrls($latestUpdate, $requestedArch);
                $downloadUrls = array_merge($downloadUrls, $macosUrls);
            }
            
            $response['download_urls'] = $downloadUrls;
            
            // 如果是macOS平台，添加架构检测信息
            if ($platform === 'macos') {
                $detector = new MacOSArchitectureDetector();
                $detection = $detector->detectArchitecture();
                $response['architecture_detection'] = $detection;
                
                // 记录检测日志
                $this->logArchitectureDetection($pdo, $detection);
            }
        }
        
        sendSuccess($response, $hasUpdate ? '发现新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        logError('检查更新失败', ['error' => $e->getMessage()]);
        sendError('检查更新失败: ' . $e->getMessage());
    }
}

/**
 * 获取macOS下载链接（支持架构）
 */
function getMacOSDownloadUrls($update, $requestedArch = null) {
    $urls = [];
    
    // 检测用户架构（如果没有手动指定）
    if (!$requestedArch) {
        $detector = new MacOSArchitectureDetector();
        $detection = $detector->detectArchitecture();
        $detectedArch = $detection['architecture'];
    } else {
        $detectedArch = $requestedArch;
    }
    
    // 根据支持类型返回链接
    switch ($update['macos_architecture_support']) {
        case 'separate':
            // 分离版本：根据检测结果返回对应链接
            if ($detectedArch === 'm1' && $update['dmg_m1_download_url']) {
                $urls['macos'] = $update['dmg_m1_download_url'];
                $urls['macos_architecture'] = 'm1';
            } elseif ($detectedArch === 'intel' && $update['dmg_intel_download_url']) {
                $urls['macos'] = $update['dmg_intel_download_url'];
                $urls['macos_architecture'] = 'intel';
            } else {
                // 未知架构或缺少对应版本，优先返回M1版本
                if ($update['dmg_m1_download_url']) {
                    $urls['macos'] = $update['dmg_m1_download_url'];
                    $urls['macos_architecture'] = 'm1';
                } elseif ($update['dmg_intel_download_url']) {
                    $urls['macos'] = $update['dmg_intel_download_url'];
                    $urls['macos_architecture'] = 'intel';
                }
            }
            
            // 同时提供所有可用版本供用户选择
            if ($update['dmg_m1_download_url']) {
                $urls['macos_m1'] = $update['dmg_m1_download_url'];
            }
            if ($update['dmg_intel_download_url']) {
                $urls['macos_intel'] = $update['dmg_intel_download_url'];
            }
            break;
            
        case 'universal':
        default:
            // 通用版本
            if ($update['dmg_download_url']) {
                $urls['macos'] = $update['dmg_download_url'];
                $urls['macos_architecture'] = 'universal';
            }
            break;
    }
    
    return $urls;
}

/**
 * 记录架构检测日志
 */
function logArchitectureDetection($pdo, $detection) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO macos_architecture_logs 
            (user_agent, detected_architecture, detection_method, ip_address) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $detection['user_agent'],
            $detection['architecture'],
            $detection['method'],
            $_SERVER['REMOTE_ADDR'] ?? null
        ]);
    } catch (Exception $e) {
        logError('记录架构检测日志失败', ['error' => $e->getMessage()]);
    }
}

/**
 * 获取智能下载链接
 */
function handleSmartDownload($pdo) {
    $updateId = $_GET['update_id'] ?? null;
    $platform = strtolower($_GET['platform'] ?? 'macos');
    $requestedArch = $_GET['architecture'] ?? null;
    
    if (!$updateId) {
        sendError('缺少版本ID参数');
    }
    
    try {
        // 获取版本信息
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ? AND status = 'published'");
        $stmt->execute([$updateId]);
        $update = $stmt->fetch();
        
        if (!$update) {
            sendError('版本不存在或未发布', 404);
        }
        
        $downloadUrl = null;
        $architecture = null;
        
        if ($platform === 'windows' && $update['exe_download_url']) {
            $downloadUrl = $update['exe_download_url'];
            $architecture = 'x64';
        } elseif ($platform === 'macos') {
            // 使用存储过程获取智能macOS下载链接
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $stmt = $pdo->prepare("CALL GetMacOSDownloadUrl(?, ?, @download_url, @architecture)");
            $stmt->execute([$updateId, $userAgent]);
            
            $result = $pdo->query("SELECT @download_url as download_url, @architecture as architecture")->fetch();
            $downloadUrl = $result['download_url'];
            $architecture = $result['architecture'];
        }
        
        if (!$downloadUrl) {
            sendError('该平台的安装包不可用', 404);
        }
        
        // 更新下载统计
        $this->updateDownloadStats($pdo, $updateId, $platform, $architecture);
        
        sendSuccess([
            'download_url' => $downloadUrl,
            'version' => $update['version'],
            'title' => $update['title'],
            'platform' => $platform,
            'architecture' => $architecture
        ], '获取下载链接成功');
        
    } catch (Exception $e) {
        logError('获取下载链接失败', ['error' => $e->getMessage()]);
        sendError('获取下载链接失败: ' . $e->getMessage());
    }
}

/**
 * 更新下载统计
 */
function updateDownloadStats($pdo, $updateId, $platform, $architecture) {
    try {
        $platformArch = $platform;
        if ($platform === 'macos' && $architecture) {
            $platformArch = 'macos_' . $architecture;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO download_stats_by_architecture (update_id, platform, download_count, last_download_at)
            VALUES (?, ?, 1, NOW())
            ON DUPLICATE KEY UPDATE 
            download_count = download_count + 1,
            last_download_at = NOW()
        ");
        $stmt->execute([$updateId, $platformArch]);
        
        // 同时更新主表的下载计数
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$updateId]);
        
    } catch (Exception $e) {
        logError('更新下载统计失败', ['error' => $e->getMessage()]);
    }
}

// API路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? 'check';

switch ($method) {
    case 'GET':
        switch ($action) {
            case 'check':
                handleCheckUpdateWithArchitecture($pdo);
                break;
            case 'download':
                handleSmartDownload($pdo);
                break;
            case 'architecture_stats':
                // 获取架构统计信息
                handleArchitectureStats($pdo);
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('不支持的请求方法', 405);
}

/**
 * 获取架构统计信息
 */
function handleArchitectureStats($pdo) {
    try {
        // 获取架构检测统计
        $stmt = $pdo->query("
            SELECT 
                detected_architecture,
                COUNT(*) as count,
                COUNT(*) * 100.0 / (SELECT COUNT(*) FROM macos_architecture_logs) as percentage
            FROM macos_architecture_logs 
            GROUP BY detected_architecture
            ORDER BY count DESC
        ");
        $architectureStats = $stmt->fetchAll();
        
        // 获取下载统计
        $stmt = $pdo->query("
            SELECT 
                platform,
                SUM(download_count) as total_downloads
            FROM download_stats_by_architecture 
            GROUP BY platform
            ORDER BY total_downloads DESC
        ");
        $downloadStats = $stmt->fetchAll();
        
        sendSuccess([
            'architecture_detection' => $architectureStats,
            'download_by_platform' => $downloadStats
        ], '获取统计信息成功');
        
    } catch (Exception $e) {
        logError('获取统计信息失败', ['error' => $e->getMessage()]);
        sendError('获取统计信息失败: ' . $e->getMessage());
    }
}

?>

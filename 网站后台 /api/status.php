<?php
/**
 * 卡密状态检查接口
 * 用于检查卡密状态
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/logs/api_errors.log');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 加载必要的文件
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/config.php';

/**
 * 卡密状态检查类
 */
class LicenseStatusChecker extends ApiBase {
    /**
     * 处理状态检查请求
     */
    public function checkStatus() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $requestData = $this->request;
        
        // 检查是否为加密数据
        if (isset($requestData['encrypted']) && $requestData['encrypted']) {
            // 解密数据
            $decryptedData = $this->decryptData($requestData['data']);
            if ($decryptedData === null) {
                return $this->respondError('Invalid encrypted data', 400);
            }
            $requestData = $decryptedData;
        }
        
        // 获取必要参数
        $key = $requestData['key'] ?? '';
        $timestamp = $requestData['timestamp'] ?? '';
        $signature = $requestData['signature'] ?? '';
        $checkStatus = $requestData['check_status'] ?? 0;
        $clientInfo = $requestData['client_info'] ?? [];
        
        // 验证必要参数
        if (empty($key)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证时间戳
        if (!empty($timestamp) && !$this->verifyTimestamp($timestamp)) {
            return $this->respondError('Invalid timestamp', 400, 'INVALID_TIMESTAMP');
        }
        
        // 验证签名
        if (!empty($signature) && !$this->verifySignature($requestData, $timestamp, $signature)) {
            return $this->respondError('Invalid signature', 400, 'INVALID_SIGNATURE');
        }
        
        // 验证卡密状态
        try {
            $stmt = $this->db->prepare("
                SELECT lk.*
                FROM license_keys lk
                WHERE lk.key_value = ?
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('License key not found', 404, 'KEY_DELETED');
            }
            
            // 检查卡密状态
            if ($keyData['status'] !== 'active') {
                return $this->respondError('License key is not active', 403, 'KEY_DISABLED');
            }
            
            // 检查是否过期
            if (strtotime($keyData['expiry_date']) <= time()) {
                return $this->respondError('License key has expired', 403, 'KEY_EXPIRED');
            }
            
            // 更新最后检查时间
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_checked_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$keyData['id']]);
            
            // 记录状态检查日志
            $stmt = $this->db->prepare("
                INSERT INTO key_status_checks 
                (key_id, ip_address, user_agent, client_info, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $keyData['id'],
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($clientInfo)
            ]);
            
            // 生成新的安全令牌
            $securityToken = bin2hex(random_bytes(16));
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '卡密状态正常',
                'security_token' => $securityToken,
                'function_type' => $keyData['type'],
                'has_customer_service' => (bool)$keyData['has_customer_service'],
                'has_product_listing' => (bool)$keyData['has_product_listing'],
                'expiry_date' => $keyData['expiry_date']
            ];
            
            // 加密响应数据
            if ($this->config->get('encryption_enabled')) {
                $encryptedData = $this->encryptData($responseData);
                return $this->respondSuccess([
                    'encrypted' => true,
                    'version' => $this->config->get('encryption_version', 'v2'),
                    'data' => $encryptedData
                ]);
            } else {
                return $this->respondSuccess($responseData);
            }
            
        } catch (PDOException $e) {
            error_log("检查卡密状态失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }

    /**
     * 加密数据
     */
    private function encryptData($data) {
        if (is_array($data)) {
            $data = json_encode($data);
        }
        
        // 使用base64编码（实际项目中应使用更安全的加密方式）
        return base64_encode($data);
    }

    /**
     * 解密数据
     */
    private function decryptData($encryptedData) {
        try {
            // 解密数据（实际项目中应使用更安全的解密方式）
            $data = base64_decode($encryptedData);
            return json_decode($data, true);
        } catch (Exception $e) {
            error_log("解密数据失败: " . $e->getMessage());
            return null;
        }
    }
}

// 创建状态检查器实例并处理请求
$checker = new LicenseStatusChecker();
$checker->checkStatus(); 
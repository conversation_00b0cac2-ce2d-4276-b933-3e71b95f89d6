<?php
/**
 * API调试工具 - 检查数据库连接和字段
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// 数据库连接信息
$host = '127.0.0.1';
$port = '3306';
$dbname = 'xiaomeihuakefu_c';
$username = 'xiaomeihuakefu_c';
$password = '7Da5F1Xx995cxYz8';

$debug_info = [];

try {
    $debug_info['step'] = 'Connecting to database';
    
    // 连接数据库
    $pdo = new PDO(
        "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
            PDO::ATTR_TIMEOUT => 10
        ]
    );
    
    $debug_info['database_connection'] = 'SUCCESS';
    
    // 设置时区
    $pdo->exec("SET time_zone = '+08:00'");
    
    $debug_info['step'] = 'Checking table structure';
    
    // 检查表结构
    $stmt = $pdo->query("DESCRIBE app_updates");
    $table_structure = $stmt->fetchAll();
    $debug_info['table_structure'] = $table_structure;
    
    // 检查必需字段是否存在
    $required_fields = [
        'version_number', 'version_name', 'version_description',
        'windows_download_url', 'macos_intel_download_url', 'macos_m1_download_url',
        'version_code', 'status', 'platform'
    ];
    
    $existing_fields = array_column($table_structure, 'Field');
    $missing_fields = array_diff($required_fields, $existing_fields);
    
    $debug_info['required_fields'] = $required_fields;
    $debug_info['existing_fields'] = $existing_fields;
    $debug_info['missing_fields'] = $missing_fields;
    
    $debug_info['step'] = 'Testing query';
    
    // 测试查询
    $stmt = $pdo->prepare("
        SELECT *, version, title, description,
               exe_download_url, dmg_intel_download_url as dmg_intel_download_url, dmg_m1_download_url as dmg_m1_download_url
        FROM app_updates 
        WHERE status = 'published' 
        AND macos_m1_download_url IS NOT NULL
        AND (platform = 'macos' OR platform = 'all')
        ORDER BY version_code DESC, created_at DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $result = $stmt->fetch();
    
    $debug_info['query_result'] = $result;
    $debug_info['query_success'] = 'SUCCESS';
    
    // 检查数据
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM app_updates");
    $count = $stmt->fetch();
    $debug_info['total_records'] = $count['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as published FROM app_updates WHERE status = 'published'");
    $published = $stmt->fetch();
    $debug_info['published_records'] = $published['published'];
    
    $debug_info['step'] = 'Complete';
    
} catch (Exception $e) {
    $debug_info['error'] = $e->getMessage();
    $debug_info['error_trace'] = $e->getTraceAsString();
    $debug_info['step_failed'] = $debug_info['step'];
}

// 返回调试信息
echo json_encode([
    'success' => !isset($debug_info['error']),
    'debug_info' => $debug_info,
    'timestamp' => date('Y-m-d H:i:s')
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
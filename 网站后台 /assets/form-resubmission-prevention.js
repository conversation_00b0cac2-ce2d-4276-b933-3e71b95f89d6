/**
 * 防止表单重新提交弹窗的全局解决方案
 * 适用于所有页面，防止浏览器在刷新页面时显示"确认重新提交表单"的弹窗
 */
(function() {
    'use strict';
    
    // 防止页面刷新时出现表单重新提交的提示
    function preventFormResubmissionDialog() {
        try {
            // 使用 history.replaceState 清除 POST 历史记录
            if (window.history && window.history.replaceState) {
                // 获取当前URL，保留查询参数但清除POST状态
                const currentUrl = window.location.href;
                window.history.replaceState(null, null, currentUrl);
            }
        } catch (error) {
            console.warn('防止表单重新提交：history.replaceState 执行失败', error);
        }
    }
    
    // 表单提交后的处理
    function handleFormSubmission() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // 跳过GET方法的表单
            if (form.method && form.method.toLowerCase() === 'get') {
                return;
            }
            
            // 避免重复绑定事件
            if (form.hasAttribute('data-resubmission-protected')) {
                return;
            }
            
            form.setAttribute('data-resubmission-protected', 'true');
            
            form.addEventListener('submit', function(e) {
                try {
                    // 标记表单已提交
                    const timestamp = Date.now();
                    sessionStorage.setItem('form_submitted_' + timestamp, 'true');
                    
                    // 延迟执行，确保表单提交完成后再清除历史记录
                    setTimeout(() => {
                        preventFormResubmissionDialog();
                    }, 100);
                } catch (error) {
                    console.warn('防止表单重新提交：表单提交处理失败', error);
                }
            });
        });
    }
    
    // 清理旧的session标记
    function cleanupSessionStorage() {
        try {
            Object.keys(sessionStorage).forEach(key => {
                if (key.startsWith('form_submitted_')) {
                    const timestamp = parseInt(key.split('_')[2]);
                    // 清理5分钟前的标记
                    if (Date.now() - timestamp > 300000) {
                        sessionStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.warn('防止表单重新提交：清理session存储失败', error);
        }
    }
    
    // 页面加载时执行的初始化函数
    function initializeFormProtection() {
        // 立即清除可能的POST状态
        preventFormResubmissionDialog();
        
        // 设置表单提交监听器
        handleFormSubmission();
        
        // 清理旧的session标记
        cleanupSessionStorage();
        
        // 监听动态添加的表单
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 检查新添加的表单
                                if (node.tagName === 'FORM') {
                                    handleFormSubmission();
                                } else if (node.querySelectorAll) {
                                    // 检查新添加元素内的表单
                                    const forms = node.querySelectorAll('form');
                                    if (forms.length > 0) {
                                        handleFormSubmission();
                                    }
                                }
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    // 页面可见性变化时也执行清理
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            preventFormResubmissionDialog();
        }
    });
    
    // 浏览器前进后退时执行清理
    window.addEventListener('popstate', function() {
        preventFormResubmissionDialog();
    });
    
    // 页面卸载前清理
    window.addEventListener('beforeunload', function() {
        cleanupSessionStorage();
    });
    
    // 根据页面加载状态选择初始化时机
    if (document.readyState === 'loading') {
        // 如果页面还在加载，等待DOM加载完成
        document.addEventListener('DOMContentLoaded', initializeFormProtection);
    } else {
        // 如果页面已经加载完成，立即执行
        initializeFormProtection();
    }
    
    // 页面完全加载后再次执行，确保所有资源都已加载
    window.addEventListener('load', function() {
        setTimeout(function() {
            preventFormResubmissionDialog();
            handleFormSubmission();
        }, 500);
    });
    
    // 导出到全局作用域，供其他脚本调用
    window.FormResubmissionPrevention = {
        init: initializeFormProtection,
        prevent: preventFormResubmissionDialog,
        cleanup: cleanupSessionStorage
    };
    
})();

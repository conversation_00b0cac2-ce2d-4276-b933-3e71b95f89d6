<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

class LanzouAjaxAPI {
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    private $cookieFile;
    
    public function __construct() {
        $this->cookieFile = '/tmp/lanzou_ajax_' . md5(uniqid()) . '.txt';
    }
    
    public function __destruct() {
        if (file_exists($this->cookieFile)) {
            @unlink($this->cookieFile);
        }
    }
    
    public function parse($url, $type = 'parse') {
        try {
            if (!$this->isValidLanzouUrl($url)) {
                return $this->error('无效的蓝奏云链接');
            }
            
            // 第1步：获取主页面并提取基本信息
            $mainHtml = $this->fetchPage($url);
            if (!$mainHtml) {
                return $this->error('获取主页面失败');
            }
            
            // 精准解析文件名
            $fileName = $this->extractFileName($mainHtml);
            
            // 提取iframe URL
            $iframeUrl = $this->extractIframeUrl($mainHtml, $url);
            if (!$iframeUrl) {
                return $this->error('未找到下载iframe');
            }
            
            // 第2步：获取iframe页面
            $iframeHtml = $this->fetchPage($iframeUrl, $url);
            if (!$iframeHtml) {
                return $this->error('获取iframe页面失败');
            }
            
            // 精准解析文件大小
            $fileSize = $this->extractFileSize($iframeHtml);
            
            // 第3步：关键 - 通过AJAX获取真实直链
            $directUrl = $this->getDirectUrlViaAjax($iframeHtml, $iframeUrl);
            
            if ($type === 'down') {
                if ($directUrl) {
                    // 直接重定向到真实下载链接
                    header('Location: ' . $directUrl);
                    exit;
                } else {
                    return $this->error('无法获取真实直链');
                }
            }
            
            // 返回解析结果
            return [
                'success' => true,
                'info' => [
                    'file_name' => $fileName,
                    'file_size' => $fileSize
                ],
                'download' => $directUrl ?: $iframeUrl,
                'fileUrl' => $directUrl
            ];
            
        } catch (Exception $e) {
            return $this->error('处理失败: ' . $e->getMessage());
        }
    }
    
    private function extractFileName($html) {
        // 精准解析：<div class="n_box_3fn" id="filenajax">文件名</div>
        if (preg_match('/<div[^>]*class="n_box_3fn"[^>]*id="filenajax"[^>]*>([^<]+)<\/div>/', $html, $matches)) {
            return trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
        }
        
        // 备用方案
        if (preg_match('/<div[^>]*id="filenajax"[^>]*>([^<]+)<\/div>/', $html, $matches)) {
            return trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
        }
        
        return '未知文件名';
    }
    
    private function extractFileSize($html) {
        // 精准解析：<div class="n_filesize">大小：106.3 M</div>
        if (preg_match('/<div[^>]*class="n_filesize"[^>]*>大小[:：]([^<]+)<\/div>/', $html, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        
        // 备用方案
        if (preg_match('/大小[:：]\s*([0-9.]+\s*[KMGTB]?[Bb]?)/', $html, $matches)) {
            return trim($matches[1]);
        }
        
        return '未知大小';
    }
    
    private function extractIframeUrl($html, $baseUrl) {
        if (preg_match('/<iframe[^>]*class="n_downlink"[^>]*src="([^"]+)"/', $html, $matches)) {
            return $this->resolveUrl($matches[1], $baseUrl);
        }
        return null;
    }
    
    private function getDirectUrlViaAjax($iframeHtml, $iframeUrl) {
        // 第1步：提取文件ID
        if (!preg_match('/ajaxm\.php\?file=(\d+)/', $iframeHtml, $matches)) {
            return null;
        }
        $fileId = $matches[1];
        
        // 第2步：提取JavaScript参数
        $params = [];
        $jsPatterns = [
            'ajaxdata' => '/var\s+ajaxdata\s*=\s*[\'"]([^\'"]*)[\'"]/',
            'wp_sign' => '/var\s+wp_sign\s*=\s*[\'"]([^\'"]*)[\'"]/',
            'kdns' => '/var\s+kdns\s*=\s*(\d+)/',
            'wsk_sign' => '/var\s+wsk_sign\s*=\s*[\'"]([^\'"]*)[\'"]/',
            'lanosso' => '/var\s+lanosso\s*=\s*[\'"]([^\'"]*)[\'"]/'
        ];
        
        foreach ($jsPatterns as $key => $pattern) {
            if (preg_match($pattern, $iframeHtml, $jsMatches)) {
                $params[$key] = $jsMatches[1];
            }
        }
        
        // 第3步：发送AJAX请求（完全模拟页面中的请求）
        $parsedUrl = parse_url($iframeUrl);
        $ajaxUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . '/ajaxm.php?file=' . $fileId;
        
        $postData = [
            'action' => 'downprocess',
            'websignkey' => $params['ajaxdata'] ?? 'IhEO',
            'signs' => $params['ajaxdata'] ?? 'IhEO',
            'sign' => $params['wp_sign'] ?? '',
            'websign' => '',
            'kd' => $params['kdns'] ?? 1,
            'ves' => 1
        ];
        
        // 第4步：发送请求并解析响应
        $response = $this->makeAjaxRequest($ajaxUrl, $postData, $iframeUrl);
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['zt']) && $data['zt'] == '1') {
                if (isset($data['dom']) && isset($data['url'])) {
                    // 构建真实下载链接，包含lanosso参数
                    $lanosso = $params['lanosso'] ?? '';
                    $directUrl = $data['dom'] . '/file/' . $data['url'] . $lanosso;
                    
                    // 验证是否是有效的直链
                    if ($this->isValidDirectUrl($directUrl)) {
                        return $directUrl;
                    }
                }
            }
        }
        
        // 第5步：如果AJAX失败，尝试从HTML中提取已有的直链
        return $this->extractExistingDirectLink($iframeHtml);
    }
    
    private function extractExistingDirectLink($iframeHtml) {
        // 尝试从HTML中提取已存在的直链
        $patterns = [
            // 精准匹配：div class="load" id="tourl" 内的 a 标签 href
            '/<div[^>]*class="load"[^>]*id="tourl"[^>]*>.*?<a[^>]*href="(https:\/\/developer-oss\.lanrar\.com\/file\/\?[^"]+)"[^>]*>/s',
            // 备用：任何包含developer-oss.lanrar.com/file/?的链接
            '/href="(https:\/\/developer-oss\.lanrar\.com\/file\/\?[^"]+)"/',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $iframeHtml, $matches)) {
                $directUrl = $matches[1];
                if ($this->isValidDirectUrl($directUrl)) {
                    return $directUrl;
                }
            }
        }
        
        return null;
    }
    
    private function makeAjaxRequest($url, $postData, $referer) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($postData),
            CURLOPT_TIMEOUT => 15,
            CURLOPT_USERAGENT => $this->userAgent,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_COOKIEJAR => $this->cookieFile,
            CURLOPT_COOKIEFILE => $this->cookieFile,
            CURLOPT_HTTPHEADER => [
                'Accept: application/json, text/javascript, */*; q=0.01',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With: XMLHttpRequest',
                'Referer: ' . $referer,
                'Origin: ' . parse_url($referer, PHP_URL_SCHEME) . '://' . parse_url($referer, PHP_URL_HOST)
            ]
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return ($httpCode === 200) ? $response : null;
    }
    
    private function fetchPage($url, $referer = null) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_USERAGENT => $this->userAgent,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_ENCODING => '',
            CURLOPT_COOKIEJAR => $this->cookieFile,
            CURLOPT_COOKIEFILE => $this->cookieFile,
            CURLOPT_HTTPHEADER => [
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding: gzip, deflate, br',
                'Cache-Control: no-cache',
                'Connection: keep-alive',
                'Upgrade-Insecure-Requests: 1'
            ]
        ]);
        
        if ($referer) {
            curl_setopt($ch, CURLOPT_REFERER, $referer);
        }
        
        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200 || !$html) {
            return false;
        }
        
        return mb_convert_encoding($html, 'UTF-8', 'auto');
    }
    
    private function isValidLanzouUrl($url) {
        $domains = ['lanzoue.com', 'lanzou.com', 'lanzoul.com', 'lanzoux.com', 'lanzoui.com', 'woozooo.com'];
        foreach ($domains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function isValidDirectUrl($url) {
        if (!$url || !filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // 精准检查：必须是developer-oss.lanrar.com/file/?开头的直链
        if (strpos($url, 'https://developer-oss.lanrar.com/file/?') === 0) {
            return true;
        }
        
        // 其他有效的下载服务器域名
        $validDomains = [
            'vip.d0.baidupan.com',
            'pc.woozooo.com',
            'webgetstore.com'
        ];
        
        foreach ($validDomains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function resolveUrl($url, $baseUrl) {
        if (empty($url) || preg_match('/^https?:\/\//', $url)) {
            return $url;
        }
        
        $parsedBase = parse_url($baseUrl);
        $scheme = $parsedBase['scheme'];
        $host = $parsedBase['host'];
        
        if (strpos($url, '/') === 0) {
            return $scheme . '://' . $host . $url;
        }
        
        return $scheme . '://' . $host . '/' . $url;
    }
    
    private function error($message) {
        return [
            'success' => false,
            'error' => $message
        ];
    }
}

// 处理请求
try {
    $url = $_GET['url'] ?? $_POST['url'] ?? '';
    $type = $_GET['type'] ?? $_POST['type'] ?? 'parse';
    
    if (empty($url)) {
        echo json_encode([
            'success' => false,
            'error' => '请提供蓝奏云链接',
            'usage' => [
                'parse' => 'lanzou_ajax.php?url=蓝奏云链接&type=parse',
                'download' => 'lanzou_ajax.php?url=蓝奏云链接&type=down'
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $api = new LanzouAjaxAPI();
    $result = $api->parse($url, $type);
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '服务器错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>